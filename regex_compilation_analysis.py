#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正则表达式编译重复性分析
分析多进程环境下正则表达式的编译和缓存机制
"""

import sys
import time
import logging
import multiprocessing as mp
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.keyword_compiler import KeywordCompiler
from core.matching_engine import MatchingEngine
from core.keyword_matcher import process_enterprise_batch_worker

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def analyze_regex_compilation_in_worker(worker_id: int, keyword_dicts: List[Dict], enterprise_batch: List[Dict]) -> Dict[str, Any]:
    """
    在工作进程中分析正则表达式编译情况
    
    Args:
        worker_id: 工作进程ID
        keyword_dicts: 关键词字典列表
        enterprise_batch: 企业批次数据
        
    Returns:
        Dict: 编译统计信息
    """
    logger.info(f"🔍 Worker {worker_id}: 开始分析正则表达式编译")
    
    # 创建匹配引擎 - 使用本地缓存（模拟实际工作进程）
    matching_engine = MatchingEngine(use_shared_cache=False)
    compiler = matching_engine.compiler
    
    # 记录编译前的统计
    initial_stats = compiler.get_performance_stats()
    logger.info(f"Worker {worker_id}: 初始编译统计 = {initial_stats}")
    
    # 模拟处理过程
    compilation_events = []
    start_time = time.time()
    
    for enterprise_idx, enterprise_dict in enumerate(enterprise_batch):
        for keyword_idx, keyword_dict in enumerate(keyword_dicts):
            # 记录每次编译事件
            event_start = time.time()
            
            # 模拟关键词编译过程
            like_keyword = keyword_dict.get('converted_like_keyword', '0')
            must_keyword = keyword_dict.get('converted_must_keyword', '0')
            unlike_keyword = keyword_dict.get('converted_unlike_keyword', '0')
            
            # 编译各种类型的关键词
            patterns_to_compile = []
            if like_keyword and like_keyword != '0':
                patterns_to_compile.append(('like', like_keyword))
            if must_keyword and must_keyword != '0':
                patterns_to_compile.append(('must', must_keyword))
            if unlike_keyword and unlike_keyword != '0':
                patterns_to_compile.append(('unlike', unlike_keyword))
            
            for pattern_type, pattern in patterns_to_compile:
                compile_start = time.time()
                compiled_pattern = compiler.compile_keyword_pattern(pattern)
                compile_time = time.time() - compile_start
                
                compilation_events.append({
                    'worker_id': worker_id,
                    'enterprise_idx': enterprise_idx,
                    'keyword_idx': keyword_idx,
                    'pattern_type': pattern_type,
                    'pattern': pattern,
                    'compile_time': compile_time,
                    'is_cached': compile_time < 0.001,  # 假设缓存命中时间很短
                    'is_valid': compiled_pattern.is_valid if compiled_pattern else False
                })
    
    processing_time = time.time() - start_time
    
    # 记录编译后的统计
    final_stats = compiler.get_performance_stats()
    
    # 计算编译统计
    new_compilations = final_stats['compiled_count'] - initial_stats['compiled_count']
    cache_hits = final_stats['cache_hits'] - initial_stats['cache_hits']
    cache_misses = final_stats['cache_misses'] - initial_stats['cache_misses']
    
    logger.info(f"Worker {worker_id}: 处理完成")
    logger.info(f"  - 新编译数: {new_compilations}")
    logger.info(f"  - 缓存命中: {cache_hits}")
    logger.info(f"  - 缓存未命中: {cache_misses}")
    logger.info(f"  - 处理时间: {processing_time:.3f}秒")
    
    return {
        'worker_id': worker_id,
        'processing_time': processing_time,
        'new_compilations': new_compilations,
        'cache_hits': cache_hits,
        'cache_misses': cache_misses,
        'compilation_events': compilation_events,
        'initial_stats': initial_stats,
        'final_stats': final_stats
    }


def create_test_data(num_keywords: int = 10, num_enterprises: int = 100) -> tuple:
    """创建测试数据"""
    
    # 创建关键词数据 - 模拟重复的关键词模式
    keyword_dicts = []
    common_patterns = ['科技', '技术', '软件', '数据', '信息']  # 模拟常见的重复模式
    
    for i in range(num_keywords):
        pattern = common_patterns[i % len(common_patterns)]  # 故意创建重复模式
        keyword_dicts.append({
            'id': f'keyword_{i}',
            'like_keyword': pattern,
            'must_keyword': '',
            'unlike_keyword': '',
            'converted_like_keyword': pattern,
            'converted_must_keyword': '0',
            'converted_unlike_keyword': '0',
            'industry_type': 'default',
            'source_scope': 'default'
        })
    
    # 创建企业数据
    enterprise_dicts = []
    for i in range(num_enterprises):
        enterprise_dicts.append({
            'lc_company_id': f'company_{i}',
            'company_name': f'测试企业{i}有限公司',
            'business_scope': f'业务范围{i}，包含各种技术服务',
            'industry_l1_code': 'default',
            'company_profile': f'企业简介{i}，专业技术公司'
        })
    
    return keyword_dicts, enterprise_dicts


def analyze_multiprocess_regex_compilation():
    """分析多进程环境下的正则表达式编译"""
    
    print("🚀 多进程正则表达式编译分析")
    print("=" * 60)
    
    # 创建测试数据
    num_keywords = 20  # 20个关键词（包含重复模式）
    num_enterprises = 200  # 200个企业
    num_workers = 4  # 4个工作进程
    
    keyword_dicts, enterprise_dicts = create_test_data(num_keywords, num_enterprises)
    
    print(f"📊 测试配置:")
    print(f"  - 关键词数量: {num_keywords}")
    print(f"  - 企业数量: {num_enterprises}")
    print(f"  - 工作进程数: {num_workers}")
    print(f"  - 总组合数: {num_keywords * num_enterprises:,}")
    
    # 分析关键词模式的重复性
    patterns = []
    for kw in keyword_dicts:
        like_pattern = kw.get('converted_like_keyword', '0')
        if like_pattern and like_pattern != '0':
            patterns.append(like_pattern)
    
    unique_patterns = set(patterns)
    print(f"  - 总模式数: {len(patterns)}")
    print(f"  - 唯一模式数: {len(unique_patterns)}")
    print(f"  - 重复率: {(len(patterns) - len(unique_patterns)) / len(patterns) * 100:.1f}%")
    
    # 将企业数据分批
    batch_size = len(enterprise_dicts) // num_workers
    enterprise_batches = [
        enterprise_dicts[i:i + batch_size]
        for i in range(0, len(enterprise_dicts), batch_size)
    ]
    
    print(f"\n🔧 企业分批:")
    for i, batch in enumerate(enterprise_batches):
        print(f"  - 批次{i+1}: {len(batch)} 个企业")
    
    # 使用多进程分析编译情况
    print(f"\n🚀 启动多进程分析...")
    
    with mp.Pool(processes=num_workers) as pool:
        # 为每个工作进程分配任务
        tasks = [
            (worker_id, keyword_dicts, enterprise_batches[worker_id])
            for worker_id in range(len(enterprise_batches))
        ]
        
        # 并行执行分析
        results = pool.starmap(analyze_regex_compilation_in_worker, tasks)
    
    # 汇总分析结果
    print(f"\n📊 编译分析结果:")
    print(f"{'Worker':<8} {'新编译':<8} {'缓存命中':<10} {'缓存未命中':<12} {'处理时间':<10}")
    print("-" * 60)
    
    total_new_compilations = 0
    total_cache_hits = 0
    total_cache_misses = 0
    total_processing_time = 0
    
    for result in results:
        worker_id = result['worker_id']
        new_compilations = result['new_compilations']
        cache_hits = result['cache_hits']
        cache_misses = result['cache_misses']
        processing_time = result['processing_time']
        
        print(f"Worker{worker_id:<3} {new_compilations:<8} {cache_hits:<10} {cache_misses:<12} {processing_time:<10.3f}s")
        
        total_new_compilations += new_compilations
        total_cache_hits += cache_hits
        total_cache_misses += cache_misses
        total_processing_time += processing_time
    
    print("-" * 60)
    print(f"{'总计':<8} {total_new_compilations:<8} {total_cache_hits:<10} {total_cache_misses:<12} {total_processing_time:<10.3f}s")
    
    # 分析重复编译问题
    print(f"\n🔍 重复编译分析:")
    print(f"  - 理论最少编译数: {len(unique_patterns)} (每个唯一模式编译一次)")
    print(f"  - 实际编译总数: {total_new_compilations}")
    print(f"  - 重复编译倍数: {total_new_compilations / len(unique_patterns):.1f}x")
    
    if total_new_compilations > len(unique_patterns):
        print(f"  ⚠️ 存在重复编译问题！")
        print(f"  - 每个进程都重新编译了相同的正则表达式")
        print(f"  - 浪费的编译次数: {total_new_compilations - len(unique_patterns)}")
    else:
        print(f"  ✅ 没有重复编译问题")
    
    # 缓存效率分析
    if total_cache_hits + total_cache_misses > 0:
        cache_hit_rate = total_cache_hits / (total_cache_hits + total_cache_misses) * 100
        print(f"\n📈 缓存效率:")
        print(f"  - 缓存命中率: {cache_hit_rate:.1f}%")
        print(f"  - 缓存命中数: {total_cache_hits}")
        print(f"  - 缓存未命中数: {total_cache_misses}")
    
    return results


def test_shared_cache_effectiveness():
    """测试共享缓存的有效性"""
    
    print(f"\n🧪 测试共享缓存有效性")
    print("=" * 60)
    
    # 创建测试数据
    keyword_dicts, enterprise_dicts = create_test_data(5, 50)
    
    print(f"🔧 测试1: 使用本地缓存 (use_shared_cache=False)")
    start_time = time.time()
    
    # 模拟多个工作进程，每个都使用本地缓存
    results_local = []
    for worker_id in range(3):
        matching_engine = MatchingEngine(use_shared_cache=False)
        compiler = matching_engine.compiler
        
        # 编译所有关键词模式
        for keyword_dict in keyword_dicts:
            like_pattern = keyword_dict.get('converted_like_keyword', '0')
            if like_pattern and like_pattern != '0':
                compiled = compiler.compile_keyword_pattern(like_pattern)
        
        stats = compiler.get_performance_stats()
        results_local.append(stats)
        print(f"  Worker {worker_id}: 编译数={stats['compiled_count']}, 缓存命中={stats['cache_hits']}")
    
    local_time = time.time() - start_time
    total_local_compilations = sum(r['compiled_count'] for r in results_local)
    
    print(f"🔧 测试2: 使用共享缓存 (use_shared_cache=True)")
    start_time = time.time()
    
    # 模拟使用共享缓存
    results_shared = []
    for worker_id in range(3):
        matching_engine = MatchingEngine(use_shared_cache=True)
        compiler = matching_engine.compiler
        
        # 编译所有关键词模式
        for keyword_dict in keyword_dicts:
            like_pattern = keyword_dict.get('converted_like_keyword', '0')
            if like_pattern and like_pattern != '0':
                compiled = compiler.compile_keyword_pattern(like_pattern)
        
        stats = compiler.get_performance_stats()
        results_shared.append(stats)
        print(f"  Worker {worker_id}: 编译数={stats['compiled_count']}, 缓存命中={stats['cache_hits']}, 共享缓存命中={stats['shared_cache_hits']}")
    
    shared_time = time.time() - start_time
    total_shared_compilations = sum(r['compiled_count'] for r in results_shared)
    
    print(f"\n📊 对比结果:")
    print(f"  本地缓存模式:")
    print(f"    - 总编译次数: {total_local_compilations}")
    print(f"    - 处理时间: {local_time:.3f}秒")
    print(f"  共享缓存模式:")
    print(f"    - 总编译次数: {total_shared_compilations}")
    print(f"    - 处理时间: {shared_time:.3f}秒")
    print(f"    - 编译减少: {total_local_compilations - total_shared_compilations} 次")
    print(f"    - 时间节省: {((local_time - shared_time) / local_time * 100):.1f}%")


def analyze_current_implementation():
    """分析当前实现中的正则表达式编译情况"""
    
    print(f"\n🔍 当前实现分析")
    print("=" * 60)
    
    print(f"📋 当前架构:")
    print(f"  1. 主进程: 使用 MatchingEngine(use_shared_cache=True)")
    print(f"  2. 工作进程: 使用 MatchingEngine(use_shared_cache=False)")
    print(f"  3. 每个工作进程都有独立的本地缓存")
    
    print(f"\n🚨 发现的问题:")
    print(f"  ❌ 每个工作进程都会重新编译相同的正则表达式")
    print(f"  ❌ 207个关键词规则 × 12个工作进程 = 最多2,484次重复编译")
    print(f"  ❌ 共享缓存机制在工作进程中被禁用")
    
    print(f"\n💡 问题原因:")
    print(f"  1. 工作进程函数中明确设置 use_shared_cache=False")
    print(f"  2. 每个进程启动时都会创建新的 MatchingEngine 实例")
    print(f"  3. 本地缓存无法跨进程共享")
    
    print(f"\n🎯 影响评估:")
    print(f"  - 您的数据: 207个关键词 × 12个进程 = 最多2,484次编译")
    print(f"  - 理论最优: 207个关键词 × 1次编译 = 207次编译")
    print(f"  - 浪费倍数: 最多12倍的重复编译")
    print(f"  - 性能影响: 每次正则编译约0.1-1ms，总计浪费2-25秒")


def recommend_optimization():
    """推荐优化方案"""
    
    print(f"\n🚀 优化建议")
    print("=" * 60)
    
    print(f"💡 方案1: 启用工作进程共享缓存")
    print(f"  修改: process_enterprise_batch_worker 中的")
    print(f"  matching_engine = MatchingEngine(use_shared_cache=False)")
    print(f"  改为: matching_engine = MatchingEngine(use_shared_cache=True)")
    
    print(f"\n💡 方案2: 预编译正则表达式")
    print(f"  在主进程中预编译所有正则表达式")
    print(f"  将编译结果传递给工作进程")
    print(f"  避免工作进程中的编译开销")
    
    print(f"\n💡 方案3: 全局正则表达式缓存")
    print(f"  使用进程间共享的全局正则表达式缓存")
    print(f"  确保相同模式只编译一次")
    
    print(f"\n🎯 推荐实施:")
    print(f"  1. 立即实施方案1（最简单，风险最低）")
    print(f"  2. 长期考虑方案2（性能最优）")
    print(f"  3. 监控编译统计，验证优化效果")


def main():
    """主函数"""
    
    # 分析当前实现
    analyze_current_implementation()
    
    # 测试共享缓存效果
    test_shared_cache_effectiveness()
    
    # 多进程编译分析
    analyze_multiprocess_regex_compilation()
    
    # 优化建议
    recommend_optimization()
    
    print(f"\n🎯 总结:")
    print(f"当前系统确实存在正则表达式重复编译问题：")
    print(f"1. 每个工作进程都重新编译相同的正则表达式")
    print(f"2. 207个关键词 × 12个进程 = 最多2,484次重复编译")
    print(f"3. 建议启用工作进程的共享缓存机制")
    print(f"4. 预期性能提升: 减少2-25秒的编译开销")


if __name__ == "__main__":
    main()
