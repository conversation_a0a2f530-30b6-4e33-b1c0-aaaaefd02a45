#!/usr/bin/env python3
"""
最终缓存移除验证测试
Final Cache Removal Verification Test

验证 _smart_parse_text_data 方法已完全移除所有缓存相关代码
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import psutil
import time
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.matching_engine import MatchingEngine


def test_no_cache_behavior():
    """测试完全移除缓存后的行为"""
    print("=" * 60)
    print("最终缓存移除验证测试")
    print("=" * 60)
    print("验证 _smart_parse_text_data 方法已完全移除所有缓存相关代码")
    print()
    
    # 创建匹配引擎实例
    engine = MatchingEngine(use_shared_cache=False)
    
    # 获取初始内存使用情况
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"初始内存使用: {initial_memory:.2f} MB")
    
    # 检查是否还有缓存相关的属性
    cache_attributes = []
    if hasattr(engine, '_text_parsing_cache'):
        cache_attributes.append('_text_parsing_cache')
    if hasattr(engine, '_text_cleaning_cache'):
        cache_attributes.append('_text_cleaning_cache')
    if hasattr(engine, '_text_cache_hit_count'):
        cache_attributes.append('_text_cache_hit_count')
    if hasattr(engine, '_text_cache_miss_count'):
        cache_attributes.append('_text_cache_miss_count')
    
    if cache_attributes:
        print(f"⚠️  发现残留的缓存属性: {cache_attributes}")
        for attr in cache_attributes:
            value = getattr(engine, attr)
            print(f"   {attr}: {value} (类型: {type(value)})")
    else:
        print("✅ 未发现缓存相关属性")
    
    print()
    
    # 测试各种数据类型的处理
    test_cases = [
        ("字符串", "企业描述信息"),
        ("numpy数组", np.array(["软件开发", "技术服务"])),
        ("Python列表", ["产品研发", "市场营销"]),
        ("元组", ("业务咨询", "技术支持")),
        ("数字", 12345),
        ("空值", None),
        ("空列表", []),
        ("JSON字符串", '["项目管理", "系统集成"]'),
    ]
    
    print("测试各种数据类型的处理:")
    for i, (desc, data) in enumerate(test_cases, 1):
        try:
            result = engine._smart_parse_text_data(data)
            print(f"{i}. {desc}: {result}")
        except Exception as e:
            print(f"{i}. {desc}: 错误 - {e}")
    
    print()
    
    # 大量数据处理测试
    print("大量数据处理测试:")
    large_data_sets = []
    for i in range(1000):
        large_data_sets.append([
            np.array([f"企业{i}业务描述", f"企业{i}技术能力"]),
            [f"企业{i}产品信息", f"企业{i}服务范围"],
            f"企业{i}简介信息",
            i
        ])
    
    start_time = time.time()
    processed_count = 0
    
    for data_set in large_data_sets:
        for data in data_set:
            result = engine._smart_parse_text_data(data)
            processed_count += 1
    
    end_time = time.time()
    final_memory = process.memory_info().rss / 1024 / 1024
    
    print(f"处理了 {processed_count} 个数据项")
    print(f"处理时间: {end_time - start_time:.3f} 秒")
    print(f"内存增长: {final_memory - initial_memory:.2f} MB")
    
    # 再次检查缓存属性
    print()
    print("处理后的缓存状态检查:")
    if cache_attributes:
        for attr in cache_attributes:
            if hasattr(engine, attr):
                value = getattr(engine, attr)
                if hasattr(value, '__len__'):
                    print(f"   {attr}: 大小 {len(value)}")
                else:
                    print(f"   {attr}: {value}")
            else:
                print(f"   {attr}: 已不存在")
    else:
        print("✅ 无缓存属性需要检查")
    
    return {
        'memory_growth': final_memory - initial_memory,
        'processing_time': end_time - start_time,
        'processed_count': processed_count,
        'cache_attributes': cache_attributes
    }


def test_method_source_code():
    """检查方法源代码中是否还有缓存相关代码"""
    print("\n" + "=" * 60)
    print("源代码检查")
    print("=" * 60)
    
    import inspect
    from core.matching_engine import MatchingEngine
    
    # 获取方法源代码
    source = inspect.getsource(MatchingEngine._smart_parse_text_data)
    
    # 检查缓存相关关键词
    cache_keywords = [
        'cache_key',
        'use_cache',
        '_text_parsing_cache',
        '_text_cleaning_cache',
        '_text_cache_hit_count',
        '_text_cache_miss_count',
        'self._text_parsing_cache',
        'self._text_cleaning_cache',
        'cache_hit',
        'cache_miss'
    ]
    
    found_keywords = []
    for keyword in cache_keywords:
        if keyword in source:
            found_keywords.append(keyword)
    
    if found_keywords:
        print(f"❌ 发现缓存相关代码: {found_keywords}")
        print("\n源代码片段:")
        lines = source.split('\n')
        for i, line in enumerate(lines, 1):
            for keyword in found_keywords:
                if keyword in line:
                    print(f"第{i}行: {line.strip()}")
                    break
    else:
        print("✅ 未发现缓存相关代码")
    
    # 统计方法行数
    lines = source.split('\n')
    non_empty_lines = [line for line in lines if line.strip()]
    print(f"\n方法总行数: {len(lines)}")
    print(f"非空行数: {len(non_empty_lines)}")
    
    return found_keywords


def main():
    """主函数"""
    print("🔧 最终缓存移除验证测试")
    print("=" * 60)
    print("验证 _smart_parse_text_data 方法已完全移除所有缓存相关代码")
    print()
    
    # 测试1：运行时行为测试
    runtime_results = test_no_cache_behavior()
    
    # 测试2：源代码检查
    source_keywords = test_method_source_code()
    
    # 最终评估
    print("\n" + "=" * 60)
    print("最终评估结果")
    print("=" * 60)
    
    success_count = 0
    total_checks = 4
    
    # 检查1：内存增长
    if runtime_results['memory_growth'] < 10:  # 小于10MB
        print("✅ 内存增长控制良好")
        success_count += 1
    else:
        print(f"❌ 内存增长过大: {runtime_results['memory_growth']:.2f} MB")
    
    # 检查2：缓存属性
    if not runtime_results['cache_attributes']:
        print("✅ 无缓存相关属性")
        success_count += 1
    else:
        print(f"❌ 发现缓存属性: {runtime_results['cache_attributes']}")
    
    # 检查3：源代码清洁
    if not source_keywords:
        print("✅ 源代码中无缓存相关代码")
        success_count += 1
    else:
        print(f"❌ 源代码中发现缓存代码: {source_keywords}")
    
    # 检查4：性能表现
    if runtime_results['processing_time'] < 1.0:  # 小于1秒
        print("✅ 处理性能良好")
        success_count += 1
    else:
        print(f"❌ 处理时间过长: {runtime_results['processing_time']:.3f} 秒")
    
    print(f"\n总体评估: {success_count}/{total_checks} 项检查通过")
    
    if success_count == total_checks:
        print("🎉 缓存移除完全成功！")
    else:
        print("⚠️  缓存移除可能不完整，需要进一步检查")
    
    print(f"\n详细统计:")
    print(f"- 处理数据项: {runtime_results['processed_count']}")
    print(f"- 处理时间: {runtime_results['processing_time']:.3f} 秒")
    print(f"- 内存增长: {runtime_results['memory_growth']:.2f} MB")
    print(f"- 平均处理速度: {runtime_results['processed_count']/runtime_results['processing_time']:.0f} 项/秒")


if __name__ == "__main__":
    main()
