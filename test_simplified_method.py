#!/usr/bin/env python3
"""
简化方法验证测试
Simplified Method Verification Test

验证简化后的 _smart_parse_text_data 方法只处理 numpy.ndarray 和 None 类型
"""

import sys
import numpy as np
from pathlib import Path
import time

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.matching_engine import MatchingEngine


def test_simplified_method():
    """测试简化后的方法"""
    print("=" * 60)
    print("简化方法验证测试")
    print("=" * 60)
    print("验证简化后的 _smart_parse_text_data 方法只处理 numpy.ndarray 和 None 类型")
    print()
    
    # 创建匹配引擎实例
    engine = MatchingEngine(use_shared_cache=False)
    
    # 测试用例：只包含 numpy.ndarray 和 None 类型
    test_cases = [
        ("None值", None),
        ("空numpy数组", np.array([])),
        ("单元素numpy数组", np.array(["技术服务"])),
        ("多元素numpy数组", np.array(["软件开发", "技术咨询"])),
        ("包含空字符串的numpy数组", np.array(["", "有效内容", "  "])),
        ("包含None的numpy数组", np.array([None, "有效内容", None])),
        ("数字numpy数组", np.array([123, 456])),
        ("混合类型numpy数组", np.array(["文本", 123, None, ""])),
    ]
    
    print("测试各种有效输入类型:")
    success_count = 0
    total_count = len(test_cases)
    
    for i, (desc, data) in enumerate(test_cases, 1):
        try:
            start_time = time.time()
            result = engine._smart_parse_text_data(data)
            end_time = time.time()
            
            print(f"{i}. {desc}:")
            print(f"   输入: {data}")
            print(f"   输出: {result}")
            print(f"   处理时间: {(end_time - start_time)*1000:.3f}ms")
            print(f"   ✅ 成功")
            success_count += 1
        except Exception as e:
            print(f"{i}. {desc}:")
            print(f"   输入: {data}")
            print(f"   ❌ 错误: {e}")
        print()
    
    print(f"测试结果: {success_count}/{total_count} 通过")
    
    return success_count == total_count


def test_performance():
    """测试性能"""
    print("=" * 60)
    print("性能测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建大量测试数据
    test_data = []
    for i in range(1000):
        test_data.append(np.array([f"企业{i}描述", f"业务{i}信息", f"技术{i}能力"]))
    
    print(f"准备了 {len(test_data)} 个numpy数组进行性能测试")
    
    start_time = time.time()
    processed_count = 0
    
    for data in test_data:
        result = engine._smart_parse_text_data(data)
        processed_count += 1
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"处理了 {processed_count} 个numpy数组")
    print(f"总处理时间: {total_time:.3f} 秒")
    print(f"平均处理时间: {(total_time/processed_count)*1000:.3f} ms/项")
    print(f"处理速度: {processed_count/total_time:.0f} 项/秒")
    
    return total_time < 1.0  # 期望在1秒内完成


def check_method_simplification():
    """检查方法简化情况"""
    print("=" * 60)
    print("方法简化检查")
    print("=" * 60)
    
    import inspect
    from core.matching_engine import MatchingEngine
    
    # 获取方法源代码
    source = inspect.getsource(MatchingEngine._smart_parse_text_data)
    
    # 检查是否还有不应该存在的代码
    removed_keywords = [
        'isinstance(col_texts, (list, tuple))',
        'isinstance(col_texts, str)',
        'isinstance(col_texts, int)',
        'json.loads',
        'JSON',
        'text_str',
        'hasattr(col_texts, \'__iter__\')',
        'list',
        'tuple'
    ]
    
    found_removed = []
    for keyword in removed_keywords:
        if keyword in source:
            found_removed.append(keyword)
    
    # 检查应该保留的代码
    required_keywords = [
        'col_texts is None',
        'numpy.ndarray',
        'Exception',
        'return []',
        'return result',
        'str(item)',
        'strip()'
    ]
    
    missing_required = []
    for keyword in required_keywords:
        if keyword not in source:
            missing_required.append(keyword)
    
    print("简化检查结果:")
    if found_removed:
        print(f"❌ 发现应该删除的代码: {found_removed}")
    else:
        print("✅ 已删除所有不必要的代码")
    
    if missing_required:
        print(f"❌ 缺少必要的代码: {missing_required}")
    else:
        print("✅ 保留了所有必要的代码")
    
    # 统计代码行数
    lines = source.split('\n')
    non_empty_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
    comment_lines = [line for line in lines if line.strip().startswith('#')]
    
    print(f"\n代码统计:")
    print(f"- 总行数: {len(lines)}")
    print(f"- 有效代码行: {len(non_empty_lines)}")
    print(f"- 注释行: {len(comment_lines)}")
    
    print(f"\n简化后的方法源代码:")
    print("-" * 40)
    print(source)
    print("-" * 40)
    
    return len(found_removed) == 0 and len(missing_required) == 0


def main():
    """主函数"""
    print("🔧 简化方法验证测试")
    print("=" * 60)
    print("验证 _smart_parse_text_data 方法简化后只处理 numpy.ndarray 和 None 类型")
    print()
    
    # 测试1：功能测试
    functional_test = test_simplified_method()
    
    # 测试2：性能测试
    performance_test = test_performance()
    
    # 测试3：简化检查
    simplification_test = check_method_simplification()
    
    # 最终评估
    print("\n" + "=" * 60)
    print("最终评估结果")
    print("=" * 60)
    
    tests = [
        ("功能测试", functional_test),
        ("性能测试", performance_test),
        ("简化检查", simplification_test)
    ]
    
    passed_tests = sum(1 for _, result in tests if result)
    total_tests = len(tests)
    
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体评估: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 方法简化完全成功！")
        print("✅ 只处理 numpy.ndarray 和 None 类型")
        print("✅ 删除了所有不必要的代码")
        print("✅ 保持了高性能")
    else:
        print("⚠️  方法简化可能不完整，需要进一步检查")


if __name__ == "__main__":
    main()
