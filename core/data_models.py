#!/usr/bin/env python3
"""
数据模型验证层
Data Models Validation Layer

使用Pydantic定义数据结构和验证规则，确保数据的完整性和正确性。

作者：系统重构
日期：2024年
"""

from typing import List, Union, Optional, Any
from pydantic import BaseModel, validator, Field
from enum import Enum


class PatternType(Enum):
    """模式类型枚举"""
    SINGLE_KEYWORD = 0  # 单个关键词
    ORDERED_MATCH = 1   # 有序匹配
    UNORDERED_MATCH = 2 # 无序匹配


class KeywordPattern(BaseModel):
    """关键词模式数据模型"""
    
    pattern: str = Field(..., description="原始模式字符串")
    pattern_type: PatternType = Field(..., description="模式类型")
    
    @validator('pattern')
    def validate_pattern(cls, v):
        """验证模式字符串"""
        if not v or not isinstance(v, str):
            raise ValueError('模式字符串不能为空')
        if len(v.strip()) == 0:
            raise ValueError('模式字符串不能仅包含空白字符')
        if len(v) > 1000:
            raise ValueError('模式字符串过长（最大1000字符）')
        return v.strip()
    
    @validator('pattern_type')
    def validate_pattern_type(cls, v):
        """验证模式类型"""
        if not isinstance(v, PatternType):
            raise ValueError('无效的模式类型')
        return v


class TransformResult(BaseModel):
    """转换结果数据模型"""
    
    pattern_type: PatternType = Field(..., description="模式类型")
    
    # 单个关键词的情况
    keyword: Optional[str] = Field(None, description="单个关键词")
    
    # 匹配模式的情况
    left_keywords: Optional[List[str]] = Field(None, description="左侧关键词列表")
    right_keywords: Optional[List[str]] = Field(None, description="右侧关键词列表")
    min_chars: Optional[int] = Field(None, description="最小字符间隔")
    max_chars: Optional[int] = Field(None, description="最大字符间隔")
    
    @validator('keyword')
    def validate_keyword(cls, v, values):
        """验证单个关键词"""
        pattern_type = values.get('pattern_type')
        if pattern_type == PatternType.SINGLE_KEYWORD:
            if not v:
                raise ValueError('单个关键词模式时，keyword字段不能为空')
            if len(v.strip()) == 0:
                raise ValueError('关键词不能仅包含空白字符')
        elif pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if v is not None:
                raise ValueError('匹配模式时，keyword字段应该为None')
        return v
    
    @validator('left_keywords')
    def validate_left_keywords(cls, v, values):
        """验证左侧关键词列表"""
        pattern_type = values.get('pattern_type')
        if pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if not v or len(v) == 0:
                raise ValueError('匹配模式时，left_keywords不能为空')
            for keyword in v:
                if not keyword or len(keyword.strip()) == 0:
                    raise ValueError('关键词不能为空或仅包含空白字符')
        elif pattern_type == PatternType.SINGLE_KEYWORD:
            if v is not None:
                raise ValueError('单个关键词模式时，left_keywords应该为None')
        return v
    
    @validator('right_keywords')
    def validate_right_keywords(cls, v, values):
        """验证右侧关键词列表"""
        pattern_type = values.get('pattern_type')
        if pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if not v or len(v) == 0:
                raise ValueError('匹配模式时，right_keywords不能为空')
            for keyword in v:
                if not keyword or len(keyword.strip()) == 0:
                    raise ValueError('关键词不能为空或仅包含空白字符')
        elif pattern_type == PatternType.SINGLE_KEYWORD:
            if v is not None:
                raise ValueError('单个关键词模式时，right_keywords应该为None')
        return v
    
    @validator('min_chars')
    def validate_min_chars(cls, v, values):
        """验证最小字符间隔"""
        pattern_type = values.get('pattern_type')
        if pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if v is None:
                raise ValueError('匹配模式时，min_chars不能为None')
            if v < 0:
                raise ValueError('最小字符间隔不能为负数')
            if v > 10000:
                raise ValueError('最小字符间隔过大（最大10000）')
        elif pattern_type == PatternType.SINGLE_KEYWORD:
            if v is not None:
                raise ValueError('单个关键词模式时，min_chars应该为None')
        return v
    
    @validator('max_chars')
    def validate_max_chars(cls, v, values):
        """验证最大字符间隔"""
        pattern_type = values.get('pattern_type')
        min_chars = values.get('min_chars')
        
        if pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if v is None:
                raise ValueError('匹配模式时，max_chars不能为None')
            if v < 0:
                raise ValueError('最大字符间隔不能为负数')
            if v > 10000:
                raise ValueError('最大字符间隔过大（最大10000）')
            if min_chars is not None and v < min_chars:
                raise ValueError('最大字符间隔不能小于最小字符间隔')
        elif pattern_type == PatternType.SINGLE_KEYWORD:
            if v is not None:
                raise ValueError('单个关键词模式时，max_chars应该为None')
        return v
    
    def to_list(self) -> List[Union[int, str, List[str]]]:
        """转换为列表格式（向后兼容）"""
        if self.pattern_type == PatternType.SINGLE_KEYWORD:
            return [0, self.keyword]
        elif self.pattern_type == PatternType.ORDERED_MATCH:
            return [1, self.left_keywords, self.right_keywords, self.min_chars, self.max_chars]
        elif self.pattern_type == PatternType.UNORDERED_MATCH:
            return [2, self.left_keywords, self.right_keywords, self.min_chars, self.max_chars]
        else:
            raise ValueError(f"未知的模式类型: {self.pattern_type}")
    
    @classmethod
    def from_list(cls, data: List[Union[int, str, List[str]]]) -> 'TransformResult':
        """从列表格式创建实例（向后兼容）"""
        if not data or len(data) < 2:
            raise ValueError("数据格式不正确")
        
        pattern_type_int = data[0]
        pattern_type = PatternType(pattern_type_int)
        
        if pattern_type == PatternType.SINGLE_KEYWORD:
            if len(data) != 2:
                raise ValueError("单个关键词模式数据格式不正确")
            return cls(
                pattern_type=pattern_type,
                keyword=data[1]
            )
        elif pattern_type in [PatternType.ORDERED_MATCH, PatternType.UNORDERED_MATCH]:
            if len(data) != 5:
                raise ValueError("匹配模式数据格式不正确")
            return cls(
                pattern_type=pattern_type,
                left_keywords=data[1],
                right_keywords=data[2],
                min_chars=data[3],
                max_chars=data[4]
            )
        else:
            raise ValueError(f"未知的模式类型: {pattern_type_int}")


class ConversionConfig(BaseModel):
    """转换配置数据模型"""
    
    max_chars: int = Field(200, description="默认最大字符间隔")
    source_file_path: str = Field("auto_detect", description="源文件路径")
    output_file_path: str = Field("converted_keyword_rules.xlsx", description="输出文件路径")
    output_sheet_name: str = Field("转换后规则", description="输出工作表名称")
    column_prefix: str = Field("converted", description="列名前缀")
    enable_logging: bool = Field(True, description="是否启用日志")
    
    @validator('max_chars')
    def validate_max_chars(cls, v):
        """验证最大字符间隔"""
        if v < 0:
            raise ValueError('最大字符间隔不能为负数')
        if v > 10000:
            raise ValueError('最大字符间隔过大（最大10000）')
        return v
    
    @validator('source_file_path')
    def validate_source_file_path(cls, v):
        """验证源文件路径"""
        if not v or len(v.strip()) == 0:
            raise ValueError('源文件路径不能为空')
        return v.strip()
    
    @validator('output_file_path')
    def validate_output_file_path(cls, v):
        """验证输出文件路径"""
        if not v or len(v.strip()) == 0:
            raise ValueError('输出文件路径不能为空')
        if not v.endswith('.xlsx'):
            raise ValueError('输出文件必须是Excel格式（.xlsx）')
        return v.strip()
    
    @validator('column_prefix')
    def validate_column_prefix(cls, v):
        """验证列名前缀"""
        if not v or len(v.strip()) == 0:
            raise ValueError('列名前缀不能为空')
        # 检查是否包含非法字符
        invalid_chars = ['/', '\\', '?', '*', '[', ']', ':']
        for char in invalid_chars:
            if char in v:
                raise ValueError(f'列名前缀不能包含字符: {char}')
        return v.strip()


class ValidationConfig(BaseModel):
    """验证配置数据模型"""
    
    max_errors_display: int = Field(10, description="最大错误显示数量")
    highlight_color: str = Field("FFFF00", description="高亮颜色")
    
    @validator('max_errors_display')
    def validate_max_errors_display(cls, v):
        """验证最大错误显示数量"""
        if v < 1:
            raise ValueError('最大错误显示数量不能小于1')
        if v > 1000:
            raise ValueError('最大错误显示数量过大（最大1000）')
        return v
    
    @validator('highlight_color')
    def validate_highlight_color(cls, v):
        """验证高亮颜色"""
        if not v or len(v.strip()) == 0:
            raise ValueError('高亮颜色不能为空')
        # 简单的颜色代码验证
        if len(v) == 6 and all(c in '0123456789ABCDEFabcdef' for c in v):
            return v.upper()
        else:
            raise ValueError('高亮颜色必须是6位十六进制颜色代码')


def validate_pattern_data(data: Any) -> TransformResult:
    """
    验证模式数据并返回TransformResult实例
    
    Args:
        data: 要验证的数据
        
    Returns:
        TransformResult: 验证后的数据模型实例
        
    Raises:
        ValidationError: 当数据验证失败时
    """
    if isinstance(data, list):
        return TransformResult.from_list(data)
    elif isinstance(data, dict):
        return TransformResult(**data)
    elif isinstance(data, TransformResult):
        return data
    else:
        raise ValueError(f"不支持的数据类型: {type(data)}")


# 测试函数
def test_data_models():
    """测试数据模型验证"""
    print("测试数据模型验证...")
    
    # 测试单个关键词模式
    try:
        result = TransformResult(
            pattern_type=PatternType.SINGLE_KEYWORD,
            keyword="汽车"
        )
        print(f"✓ 单个关键词模式验证通过: {result.to_list()}")
    except Exception as e:
        print(f"❌ 单个关键词模式验证失败: {e}")
    
    # 测试有序匹配模式
    try:
        result = TransformResult(
            pattern_type=PatternType.ORDERED_MATCH,
            left_keywords=["新能源"],
            right_keywords=["汽车"],
            min_chars=0,
            max_chars=200
        )
        print(f"✓ 有序匹配模式验证通过: {result.to_list()}")
    except Exception as e:
        print(f"❌ 有序匹配模式验证失败: {e}")
    
    # 测试从列表创建
    try:
        result = TransformResult.from_list([1, ["新能源"], ["汽车"], 0, 200])
        print(f"✓ 从列表创建验证通过: {result.to_list()}")
    except Exception as e:
        print(f"❌ 从列表创建验证失败: {e}")
    
    # 测试配置验证
    try:
        config = ConversionConfig(
            max_chars=300,
            output_file_path="test.xlsx"
        )
        print(f"✓ 配置验证通过: max_chars={config.max_chars}")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")


if __name__ == "__main__":
    test_data_models() 