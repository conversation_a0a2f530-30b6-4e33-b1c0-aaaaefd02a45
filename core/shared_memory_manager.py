#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
共享内存管理器 - 内存映射技术优化
使用 multiprocessing.shared_memory 替代 Manager().dict() 提升性能
"""

import sys
import pickle
import hashlib
import logging
import threading
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from multiprocessing import shared_memory
import numpy as np

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class SharedMemoryBlock:
    """共享内存块信息"""
    name: str
    size: int
    data_type: str  # 'regex_cache', 'company_data', 'keyword_data'
    created_at: float
    access_count: int = 0


class SharedMemoryManager:
    """
    共享内存管理器
    
    负责管理正则表达式缓存和企业数据的共享内存存储，
    提供高效的进程间数据共享机制。
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._memory_blocks: Dict[str, SharedMemoryBlock] = {}
        self._memory_objects: Dict[str, shared_memory.SharedMemory] = {}
        self._lock = threading.RLock()
        
        # 性能统计
        self.stats = {
            'blocks_created': 0,
            'blocks_accessed': 0,
            'total_memory_allocated': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        self.logger.info("共享内存管理器初始化完成")
    
    def create_regex_cache_block(self, cache_data: Dict[str, Any], block_name: str = None) -> str:
        """
        创建正则表达式缓存的共享内存块
        
        Args:
            cache_data: 要缓存的正则表达式数据
            block_name: 可选的块名称
            
        Returns:
            str: 共享内存块名称
        """
        if block_name is None:
            # 基于数据内容生成唯一名称
            data_hash = hashlib.md5(str(cache_data).encode()).hexdigest()[:16]
            block_name = f"regex_cache_{data_hash}"
        
        with self._lock:
            try:
                # 序列化数据
                serialized_data = pickle.dumps(cache_data)
                data_size = len(serialized_data)
                
                # 创建共享内存块
                shm = shared_memory.SharedMemory(create=True, size=data_size, name=block_name)
                
                # 写入数据
                shm.buf[:data_size] = serialized_data
                
                # 记录块信息
                import time
                block_info = SharedMemoryBlock(
                    name=block_name,
                    size=data_size,
                    data_type='regex_cache',
                    created_at=time.time()
                )
                
                self._memory_blocks[block_name] = block_info
                self._memory_objects[block_name] = shm
                
                self.stats['blocks_created'] += 1
                self.stats['total_memory_allocated'] += data_size
                
                self.logger.info(f"创建正则缓存共享内存块: {block_name}, 大小: {data_size} bytes")
                return block_name
                
            except Exception as e:
                self.logger.error(f"创建正则缓存共享内存块失败: {e}")
                raise
    
    def create_company_data_block(self, company_data: List[Dict[str, Any]], block_name: str = None) -> str:
        """
        创建企业数据的共享内存块
        
        Args:
            company_data: 企业数据列表
            block_name: 可选的块名称
            
        Returns:
            str: 共享内存块名称
        """
        if block_name is None:
            data_hash = hashlib.md5(str(len(company_data)).encode()).hexdigest()[:16]
            block_name = f"company_data_{data_hash}"
        
        with self._lock:
            try:
                # 提取关键字段到共享内存
                optimized_data = []
                for company in company_data:
                    # 只保存关键字段，减少内存占用
                    optimized_company = {
                        'lc_company_id': company.get('lc_company_id', ''),
                        'company_name': company.get('company_name', ''),
                        'business_scope': company.get('business_scope', ''),
                        'company_profile': company.get('company_profile', ''),
                        'industry_l1_code': company.get('industry_l1_code', 'default')
                    }
                    optimized_data.append(optimized_company)
                
                # 序列化数据
                serialized_data = pickle.dumps(optimized_data)
                data_size = len(serialized_data)
                
                # 创建共享内存块
                shm = shared_memory.SharedMemory(create=True, size=data_size, name=block_name)
                
                # 写入数据
                shm.buf[:data_size] = serialized_data
                
                # 记录块信息
                import time
                block_info = SharedMemoryBlock(
                    name=block_name,
                    size=data_size,
                    data_type='company_data',
                    created_at=time.time()
                )
                
                self._memory_blocks[block_name] = block_info
                self._memory_objects[block_name] = shm
                
                self.stats['blocks_created'] += 1
                self.stats['total_memory_allocated'] += data_size
                
                self.logger.info(f"创建企业数据共享内存块: {block_name}, 大小: {data_size} bytes, 企业数: {len(optimized_data)}")
                return block_name
                
            except Exception as e:
                self.logger.error(f"创建企业数据共享内存块失败: {e}")
                raise
    
    def get_data_from_block(self, block_name: str) -> Optional[Any]:
        """
        从共享内存块获取数据
        
        Args:
            block_name: 共享内存块名称
            
        Returns:
            Any: 反序列化的数据，如果失败返回None
        """
        with self._lock:
            try:
                if block_name not in self._memory_blocks:
                    self.stats['cache_misses'] += 1
                    return None
                
                # 获取共享内存对象
                if block_name in self._memory_objects:
                    shm = self._memory_objects[block_name]
                else:
                    # 尝试连接到现有的共享内存块
                    shm = shared_memory.SharedMemory(name=block_name)
                    self._memory_objects[block_name] = shm
                
                # 读取并反序列化数据
                block_info = self._memory_blocks[block_name]
                data_bytes = bytes(shm.buf[:block_info.size])
                data = pickle.loads(data_bytes)
                
                # 更新统计
                block_info.access_count += 1
                self.stats['blocks_accessed'] += 1
                self.stats['cache_hits'] += 1
                
                return data
                
            except Exception as e:
                self.logger.debug(f"从共享内存块获取数据失败: {block_name}, 错误: {e}")
                self.stats['cache_misses'] += 1
                return None
    
    def update_regex_cache(self, block_name: str, cache_key: str, cache_value: Any) -> bool:
        """
        更新正则表达式缓存
        
        Args:
            block_name: 共享内存块名称
            cache_key: 缓存键
            cache_value: 缓存值
            
        Returns:
            bool: 是否更新成功
        """
        with self._lock:
            try:
                # 获取现有数据
                current_data = self.get_data_from_block(block_name)
                if current_data is None:
                    current_data = {}
                
                # 更新数据
                current_data[cache_key] = cache_value
                
                # 重新创建共享内存块（因为大小可能变化）
                self.cleanup_block(block_name)
                new_block_name = self.create_regex_cache_block(current_data, block_name)
                
                return new_block_name == block_name
                
            except Exception as e:
                self.logger.error(f"更新正则缓存失败: {e}")
                return False
    
    def cleanup_block(self, block_name: str) -> bool:
        """
        清理共享内存块
        
        Args:
            block_name: 要清理的块名称
            
        Returns:
            bool: 是否清理成功
        """
        with self._lock:
            try:
                if block_name in self._memory_objects:
                    shm = self._memory_objects[block_name]
                    shm.close()
                    shm.unlink()  # 删除共享内存
                    del self._memory_objects[block_name]
                
                if block_name in self._memory_blocks:
                    block_info = self._memory_blocks[block_name]
                    self.stats['total_memory_allocated'] -= block_info.size
                    del self._memory_blocks[block_name]
                
                self.logger.debug(f"清理共享内存块: {block_name}")
                return True
                
            except Exception as e:
                self.logger.error(f"清理共享内存块失败: {block_name}, 错误: {e}")
                return False
    
    def cleanup_all(self):
        """清理所有共享内存块"""
        with self._lock:
            block_names = list(self._memory_blocks.keys())
            for block_name in block_names:
                self.cleanup_block(block_name)
            
            self.logger.info("清理所有共享内存块完成")
    
    def get_memory_stats(self) -> Dict[str, Any]:
        """获取内存使用统计"""
        with self._lock:
            return {
                'total_blocks': len(self._memory_blocks),
                'total_memory_mb': self.stats['total_memory_allocated'] / 1024 / 1024,
                'blocks_by_type': {
                    data_type: len([b for b in self._memory_blocks.values() if b.data_type == data_type])
                    for data_type in ['regex_cache', 'company_data', 'keyword_data']
                },
                'performance_stats': self.stats.copy()
            }


# 全局共享内存管理器实例
_global_memory_manager: Optional[SharedMemoryManager] = None
_manager_lock = threading.Lock()


def get_shared_memory_manager() -> SharedMemoryManager:
    """获取全局共享内存管理器实例"""
    global _global_memory_manager
    
    with _manager_lock:
        if _global_memory_manager is None:
            _global_memory_manager = SharedMemoryManager()
        return _global_memory_manager


def cleanup_shared_memory():
    """清理全局共享内存"""
    global _global_memory_manager
    
    with _manager_lock:
        if _global_memory_manager is not None:
            _global_memory_manager.cleanup_all()
            _global_memory_manager = None
