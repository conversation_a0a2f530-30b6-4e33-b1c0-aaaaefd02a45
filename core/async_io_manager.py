#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异步I/O管理器 - 异步处理架构优化
使用 asyncio 优化I/O密集型操作，提升整体性能
"""

import sys
import asyncio
import aiofiles
import logging
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import pandas as pd
import json
from concurrent.futures import ThreadPoolExecutor
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class AsyncIOManager:
    """
    异步I/O管理器
    
    负责管理异步文件读写操作，优化I/O密集型任务的性能。
    """
    
    def __init__(self, max_concurrent_files: int = 10):
        """
        初始化异步I/O管理器
        
        Args:
            max_concurrent_files: 最大并发文件操作数
        """
        self.logger = logging.getLogger(__name__)
        self.max_concurrent_files = max_concurrent_files
        self._semaphore = asyncio.Semaphore(max_concurrent_files)
        self._thread_pool = ThreadPoolExecutor(max_workers=4)
        
        # 性能统计
        self.stats = {
            'files_read': 0,
            'files_written': 0,
            'total_read_time': 0.0,
            'total_write_time': 0.0,
            'concurrent_operations': 0,
            'max_concurrent_operations': 0
        }
        
        self.logger.info(f"异步I/O管理器初始化完成，最大并发数: {max_concurrent_files}")
    
    async def read_parquet_file_async(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        异步读取Parquet文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            pd.DataFrame: 读取的数据，失败时返回None
        """
        async with self._semaphore:
            self.stats['concurrent_operations'] += 1
            self.stats['max_concurrent_operations'] = max(
                self.stats['max_concurrent_operations'],
                self.stats['concurrent_operations']
            )
            
            try:
                start_time = time.time()
                
                # 在线程池中执行CPU密集型的parquet读取
                loop = asyncio.get_event_loop()
                df = await loop.run_in_executor(
                    self._thread_pool,
                    pd.read_parquet,
                    file_path
                )
                
                read_time = time.time() - start_time
                self.stats['files_read'] += 1
                self.stats['total_read_time'] += read_time
                
                self.logger.debug(f"异步读取Parquet文件: {file_path}, 耗时: {read_time:.3f}秒, 行数: {len(df)}")
                return df
                
            except Exception as e:
                self.logger.error(f"异步读取Parquet文件失败: {file_path}, 错误: {e}")
                return None
            finally:
                self.stats['concurrent_operations'] -= 1
    
    async def read_excel_file_async(self, file_path: str, sheet_name: str = None) -> Optional[pd.DataFrame]:
        """
        异步读取Excel文件
        
        Args:
            file_path: 文件路径
            sheet_name: 工作表名称
            
        Returns:
            pd.DataFrame: 读取的数据，失败时返回None
        """
        async with self._semaphore:
            self.stats['concurrent_operations'] += 1
            self.stats['max_concurrent_operations'] = max(
                self.stats['max_concurrent_operations'],
                self.stats['concurrent_operations']
            )
            
            try:
                start_time = time.time()
                
                # 在线程池中执行CPU密集型的Excel读取
                loop = asyncio.get_event_loop()
                if sheet_name:
                    df = await loop.run_in_executor(
                        self._thread_pool,
                        lambda: pd.read_excel(file_path, sheet_name=sheet_name)
                    )
                else:
                    df = await loop.run_in_executor(
                        self._thread_pool,
                        pd.read_excel,
                        file_path
                    )
                
                read_time = time.time() - start_time
                self.stats['files_read'] += 1
                self.stats['total_read_time'] += read_time
                
                self.logger.debug(f"异步读取Excel文件: {file_path}, 耗时: {read_time:.3f}秒, 行数: {len(df)}")
                return df
                
            except Exception as e:
                self.logger.error(f"异步读取Excel文件失败: {file_path}, 错误: {e}")
                return None
            finally:
                self.stats['concurrent_operations'] -= 1
    
    async def write_csv_file_async(self, df: pd.DataFrame, file_path: str, **kwargs) -> bool:
        """
        异步写入CSV文件
        
        Args:
            df: 要写入的DataFrame
            file_path: 文件路径
            **kwargs: 传递给to_csv的额外参数
            
        Returns:
            bool: 是否写入成功
        """
        async with self._semaphore:
            self.stats['concurrent_operations'] += 1
            self.stats['max_concurrent_operations'] = max(
                self.stats['max_concurrent_operations'],
                self.stats['concurrent_operations']
            )
            
            try:
                start_time = time.time()
                
                # 在线程池中执行CPU密集型的CSV写入
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self._thread_pool,
                    lambda: df.to_csv(file_path, **kwargs)
                )
                
                write_time = time.time() - start_time
                self.stats['files_written'] += 1
                self.stats['total_write_time'] += write_time
                
                self.logger.debug(f"异步写入CSV文件: {file_path}, 耗时: {write_time:.3f}秒, 行数: {len(df)}")
                return True
                
            except Exception as e:
                self.logger.error(f"异步写入CSV文件失败: {file_path}, 错误: {e}")
                return False
            finally:
                self.stats['concurrent_operations'] -= 1
    
    async def write_excel_file_async(self, df: pd.DataFrame, file_path: str, sheet_name: str = 'Sheet1', **kwargs) -> bool:
        """
        异步写入Excel文件
        
        Args:
            df: 要写入的DataFrame
            file_path: 文件路径
            sheet_name: 工作表名称
            **kwargs: 传递给to_excel的额外参数
            
        Returns:
            bool: 是否写入成功
        """
        async with self._semaphore:
            self.stats['concurrent_operations'] += 1
            self.stats['max_concurrent_operations'] = max(
                self.stats['max_concurrent_operations'],
                self.stats['concurrent_operations']
            )
            
            try:
                start_time = time.time()
                
                # 在线程池中执行CPU密集型的Excel写入
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self._thread_pool,
                    lambda: df.to_excel(file_path, sheet_name=sheet_name, **kwargs)
                )
                
                write_time = time.time() - start_time
                self.stats['files_written'] += 1
                self.stats['total_write_time'] += write_time
                
                self.logger.debug(f"异步写入Excel文件: {file_path}, 耗时: {write_time:.3f}秒, 行数: {len(df)}")
                return True
                
            except Exception as e:
                self.logger.error(f"异步写入Excel文件失败: {file_path}, 错误: {e}")
                return False
            finally:
                self.stats['concurrent_operations'] -= 1
    
    async def write_json_file_async(self, data: Union[Dict, List], file_path: str, **kwargs) -> bool:
        """
        异步写入JSON文件
        
        Args:
            data: 要写入的数据
            file_path: 文件路径
            **kwargs: 传递给json.dump的额外参数
            
        Returns:
            bool: 是否写入成功
        """
        async with self._semaphore:
            self.stats['concurrent_operations'] += 1
            self.stats['max_concurrent_operations'] = max(
                self.stats['max_concurrent_operations'],
                self.stats['concurrent_operations']
            )
            
            try:
                start_time = time.time()
                
                # 使用aiofiles进行异步文件写入
                async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                    json_str = json.dumps(data, ensure_ascii=False, **kwargs)
                    await f.write(json_str)
                
                write_time = time.time() - start_time
                self.stats['files_written'] += 1
                self.stats['total_write_time'] += write_time
                
                self.logger.debug(f"异步写入JSON文件: {file_path}, 耗时: {write_time:.3f}秒")
                return True
                
            except Exception as e:
                self.logger.error(f"异步写入JSON文件失败: {file_path}, 错误: {e}")
                return False
            finally:
                self.stats['concurrent_operations'] -= 1
    
    async def batch_read_parquet_files(self, file_paths: List[str]) -> List[Optional[pd.DataFrame]]:
        """
        批量异步读取Parquet文件
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            List[Optional[pd.DataFrame]]: 读取结果列表
        """
        self.logger.info(f"开始批量异步读取 {len(file_paths)} 个Parquet文件")
        start_time = time.time()
        
        # 创建异步任务
        tasks = [self.read_parquet_file_async(path) for path in file_paths]
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                self.logger.error(f"读取文件失败: {file_paths[i]}, 错误: {result}")
                processed_results.append(None)
            else:
                processed_results.append(result)
        
        total_time = time.time() - start_time
        successful_reads = sum(1 for r in processed_results if r is not None)
        
        self.logger.info(f"批量读取完成: {successful_reads}/{len(file_paths)} 成功, 耗时: {total_time:.3f}秒")
        
        return processed_results
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        avg_read_time = self.stats['total_read_time'] / max(self.stats['files_read'], 1)
        avg_write_time = self.stats['total_write_time'] / max(self.stats['files_written'], 1)
        
        return {
            'files_read': self.stats['files_read'],
            'files_written': self.stats['files_written'],
            'avg_read_time': avg_read_time,
            'avg_write_time': avg_write_time,
            'max_concurrent_operations': self.stats['max_concurrent_operations'],
            'total_read_time': self.stats['total_read_time'],
            'total_write_time': self.stats['total_write_time']
        }
    
    def cleanup(self):
        """清理资源"""
        try:
            self._thread_pool.shutdown(wait=True)
            self.logger.info("异步I/O管理器资源清理完成")
        except Exception as e:
            self.logger.error(f"清理异步I/O管理器资源时发生错误: {e}")


# 全局异步I/O管理器实例
_global_async_io_manager: Optional[AsyncIOManager] = None
_manager_lock = threading.Lock()


def get_async_io_manager() -> AsyncIOManager:
    """获取全局异步I/O管理器实例"""
    global _global_async_io_manager
    
    with _manager_lock:
        if _global_async_io_manager is None:
            _global_async_io_manager = AsyncIOManager()
        return _global_async_io_manager


def cleanup_async_io():
    """清理全局异步I/O管理器"""
    global _global_async_io_manager
    
    with _manager_lock:
        if _global_async_io_manager is not None:
            _global_async_io_manager.cleanup()
            _global_async_io_manager = None
