#!/usr/bin/env python3
"""
关键词匹配器主控制模块
Keyword Matcher Main Controller Module

该模块是智能关键词匹配功能的主控制器，协调各个子模块完成整体匹配流程。
实现choice=4的核心功能，提供完整的关键词匹配和标记服务。

主要功能：
1. 读取关键词表格和输入数据表格
2. 协调筛选逻辑、匹配引擎和结果处理
3. 提供批量处理和性能优化
4. 生成详细的匹配报告

作者：系统开发
日期：2024年
"""

import pandas as pd
import logging
import psutil
import time
from typing import List, Dict, Set, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor, as_completed
import sys
import os
import random
from pathlib import Path
import asyncio

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import config_manager


class PerformanceProfiler:
    """性能分析器 - 用于深入分析execute_matching方法的性能瓶颈"""

    def __init__(self):
        self.start_time = None
        self.step_times = {}
        self.memory_usage = {}
        self.counters = {}
        self.process = psutil.Process()

        # 性能统计
        self.regex_compile_count = 0
        self.regex_match_count = 0
        self.dataframe_to_dict_count = 0
        self.filter_apply_count = 0
        self.text_processing_count = 0

    def start_timing(self, step_name: str):
        """开始计时"""
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time

        self.step_times[step_name] = {'start': current_time}

        # 记录内存使用
        memory_info = self.process.memory_info()
        self.memory_usage[step_name] = {
            'start_rss': memory_info.rss / 1024 / 1024,  # MB
            'start_vms': memory_info.vms / 1024 / 1024   # MB
        }

    def end_timing(self, step_name: str):
        """结束计时"""
        current_time = time.time()
        if step_name in self.step_times:
            self.step_times[step_name]['end'] = current_time
            self.step_times[step_name]['duration'] = current_time - self.step_times[step_name]['start']

            # 记录结束时的内存使用
            memory_info = self.process.memory_info()
            self.memory_usage[step_name].update({
                'end_rss': memory_info.rss / 1024 / 1024,
                'end_vms': memory_info.vms / 1024 / 1024
            })

            # 计算内存增长
            self.memory_usage[step_name]['rss_growth'] = (
                self.memory_usage[step_name]['end_rss'] -
                self.memory_usage[step_name]['start_rss']
            )

    def increment_counter(self, counter_name: str, count: int = 1):
        """增加计数器"""
        self.counters[counter_name] = self.counters.get(counter_name, 0) + count

    def get_total_time(self) -> float:
        """获取总耗时"""
        if self.start_time is None:
            return 0
        return time.time() - self.start_time

    def get_step_duration(self, step_name: str) -> float:
        """获取步骤耗时"""
        return self.step_times.get(step_name, {}).get('duration', 0)

    def get_memory_growth(self, step_name: str) -> float:
        """获取内存增长"""
        return self.memory_usage.get(step_name, {}).get('rss_growth', 0)

    def log_performance_summary(self, logger):
        """记录性能摘要"""
        total_time = self.get_total_time()
        logger.info("🚀 性能监控摘要:")
        logger.info(f"  总耗时: {total_time:.2f}秒")

        for step_name, timing in self.step_times.items():
            duration = timing.get('duration', 0)
            percentage = (duration / total_time * 100) if total_time > 0 else 0
            memory_growth = self.get_memory_growth(step_name)
            logger.info(f"  {step_name}: {duration:.2f}秒 ({percentage:.1f}%), 内存增长: {memory_growth:.1f}MB")

    def get_cache_performance(self) -> Dict[str, Any]:
        """获取缓存性能统计"""
        return {
            'step_count': len(self.step_times),
            'total_time': self.get_total_time(),
            'memory_peak': max([mem.get('rss_mb', 0) for mem in self.memory_usage.values()], default=0)
        }


def process_enterprise_batch_worker(enterprise_batch: List[Dict], keyword_dicts: List[Dict]) -> List[Dict[str, Any]]:
    """
    🚀 数据分片优化：按企业数据分片的工作函数

    每个进程处理完整的企业数据子集，避免重复数据传输和序列化开销

    Args:
        enterprise_batch: 企业数据批次列表
        keyword_dicts: 关键词字典列表（所有进程共享）

    Returns:
        List[Dict[str, Any]]: 匹配结果列表
    """
    batch_results = []

    # 🚀 优化：在工作进程中初始化组件，使用本地缓存避免进程间通信开销
    from core.filter_logic import FilterLogic
    from core.matching_engine import MatchingEngine

    filter_logic = FilterLogic()
    # 🚀 实际情况：多进程环境中共享缓存有技术限制，使用本地缓存但优化编译策略
    matching_engine = MatchingEngine(use_shared_cache=False)

    # 🚀 数据分片优化：每个企业与所有关键词进行匹配
    for input_dict in enterprise_batch:
        if not input_dict:
            continue

        # 🚀 优化：对当前企业数据与所有关键词进行匹配
        for keyword_dict in keyword_dicts:
            if not keyword_dict:
                continue

            try:
                # 🚀 快速预筛选：避免不必要的深度处理
                if not input_dict.get('company_name') or not keyword_dict.get('like_keyword'):
                    continue

                # 应用筛选逻辑
                filter_result = filter_logic.apply_combined_filter(keyword_dict, input_dict)
                if not filter_result.passed:
                    continue

                # 执行关键词匹配
                match_result = matching_engine.match_keywords(
                    keyword_dict, input_dict, filter_result.filtered_columns
                )

                # 🚀 优化：构建精简的结果记录，减少内存占用
                match_details = match_result.match_details or {}
                stage_stats = match_details.get('stage_stats', {})

                result = {
                    # 标识信息
                    'keyword_index': keyword_dict.get('id', ''),
                    'lc_company_id': input_dict.get('lc_company_id', ''),
                    'company_name': input_dict.get('company_name', ''),

                    # 关键词信息
                    'keyword_id': keyword_dict.get('id', ''),
                    'like_keyword': keyword_dict.get('like_keyword', ''),
                    'must_keyword': keyword_dict.get('must_keyword', ''),
                    'unlike_keyword': keyword_dict.get('unlike_keyword', ''),

                    # 匹配结果信息
                    'match_success': match_result.success,
                    'matched_texts': match_result.matched_texts,
                    'match_details': match_details,
                    'failure_reason': match_result.failure_reason if not match_result.success else None,

                    # 展开的统计信息字段
                    'total_texts_processed': match_details.get('total_texts_processed', 0),
                    'texts_passed_all_stages': match_details.get('texts_passed_all_stages', 0),
                    'like_passed_count': stage_stats.get('like_passed', 0),
                    'must_passed_count': stage_stats.get('must_passed', 0),
                    'unlike_passed_count': stage_stats.get('unlike_passed', 0),
                    'like_failed_count': stage_stats.get('like_failed', 0),
                    'must_failed_count': stage_stats.get('must_failed', 0),
                    'unlike_failed_count': stage_stats.get('unlike_failed', 0)
                }
                batch_results.append(result)

            except Exception as e:
                # 🚀 优化：减少日志开销，只在调试模式下记录详细错误
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"处理企业数据失败 (company_id={input_dict.get('lc_company_id', 'unknown')}): {e}")
                continue

    return batch_results


# 🚀 向后兼容：保持原函数名的别名，适配参数
def process_optimized_batch_worker(index_batch: List[Tuple], keyword_dicts: List[Dict], input_dicts: List[Dict]) -> List[Dict[str, Any]]:
    """
    向后兼容的批处理工作函数 - 适配原有的参数格式
    """
    # 将索引批次转换为企业批次格式
    enterprise_batch = []
    for keyword_idx, input_idx in index_batch:
        if input_idx < len(input_dicts):
            enterprise_batch.append(input_dicts[input_idx])

    # 调用新的企业分片函数
    return process_enterprise_batch_worker(enterprise_batch, keyword_dicts)


def process_combination_batch_worker(combination_batch: List[Tuple]) -> List[Dict[str, Any]]:
    """
    传统的批处理工作函数 - 用于进程池处理（保持向后兼容）

    Args:
        combination_batch: 包含(keyword_idx, keyword_row_dict, input_row_dict)元组的列表

    Returns:
        List[Dict[str, Any]]: 匹配结果列表
    """
    batch_results = []

    # 在工作进程中重新初始化组件
    from core.filter_logic import FilterLogic
    from core.matching_engine import MatchingEngine
    from config import config_manager

    filter_logic = FilterLogic()
    matching_engine = MatchingEngine()

    # 获取标识列配置
    identifier_columns = config_manager.get_list('keyword_matching.input_table_columns.identifier_columns')

    for keyword_idx, keyword_row_dict, input_row_dict in combination_batch:
        try:
            # 应用筛选逻辑
            filter_result = filter_logic.apply_combined_filter(keyword_row_dict, input_row_dict)
            if not filter_result.passed:
                continue

            # 执行关键词匹配
            match_result = matching_engine.match_keywords(
                keyword_row_dict, input_row_dict, filter_result.filtered_columns
            )

            # 构建结果字典 - 包括成功和失败的匹配
            result = {
                'keyword_index': keyword_idx,
                'keyword_rule_id': keyword_row_dict.get('id', keyword_idx),
                'keyword_rule_name': keyword_row_dict.get('name', f'规则{keyword_idx}'),
                'match_success': match_result.success,
                'matched_texts': match_result.matched_texts,
                'match_details': match_result.match_details,  # 添加完整的match_details
                'failure_reason': match_result.failure_reason if not match_result.success else None,
                'total_texts_processed': match_result.match_details.get('total_texts_processed', 0),
                'texts_passed_all_stages': match_result.match_details.get('texts_passed_all_stages', 0),
                'like_passed_count': match_result.match_details.get('stage_stats', {}).get('like_passed', 0),
                'must_passed_count': match_result.match_details.get('stage_stats', {}).get('must_passed', 0),
                'unlike_passed_count': match_result.match_details.get('stage_stats', {}).get('unlike_passed', 0),
                'like_failed_count': match_result.match_details.get('stage_stats', {}).get('like_failed', 0),
                'must_failed_count': match_result.match_details.get('stage_stats', {}).get('must_failed', 0),
                'unlike_failed_count': match_result.match_details.get('stage_stats', {}).get('unlike_failed', 0)
            }

            # 添加输入数据的标识列
            for col in identifier_columns:
                if col in input_row_dict:
                    result[col] = input_row_dict[col]

            # 添加配置化的标识字段（用于过滤验证）
            # 从配置获取字段名称
            keyword_identifier_field = config_manager.get_str('keyword_matching.result_fields.keyword_identifier.field_name', 'keyword_index')
            company_identifier_field = config_manager.get_str('keyword_matching.result_fields.company_identifier.field_name', 'company_id')

            # 设置配置化字段
            result[keyword_identifier_field] = keyword_idx
            result[company_identifier_field] = input_row_dict.get('lc_company_id', '')

            batch_results.append(result)

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")

    return batch_results
from core.filter_logic import FilterLogic
from core.matching_engine import MatchingEngine
from core.result_processor import ResultProcessor
from core.shared_memory_manager import get_shared_memory_manager, cleanup_shared_memory
from core.async_io_manager import get_async_io_manager, cleanup_async_io
from utils.progress_bar import create_progress_bar, ProgressBar


@dataclass
class MatchingProgress:
    """匹配进度数据类"""
    total_combinations: int = 0
    processed_combinations: int = 0
    successful_matches: int = 0
    failed_matches: int = 0
    current_input_row: int = 0
    total_input_rows: int = 0


class KeywordMatcher:
    """
    关键词匹配器主控制类
    
    协调各个子模块完成智能关键词匹配的完整流程，
    提供高效的批量处理和详细的进度跟踪。
    """
    
    def __init__(self):
        """初始化关键词匹配器"""
        self.logger = logging.getLogger(__name__)

        # 🚀 优化：初始化子模块，使用本地缓存避免多进程冲突
        self.filter_logic = FilterLogic()
        self.matching_engine = MatchingEngine(use_shared_cache=False)
        self.result_processor = ResultProcessor()

        # 从配置获取设置
        self.batch_size = config_manager.get_int('keyword_matching.batch_size', 1000)
        self.max_workers = config_manager.get_int('keyword_matching.max_workers', 4)
        self.enable_optimization = config_manager.get_bool('keyword_matching.enable_performance_optimization', True)

        # 🚀 优化：并行处理方式配置：'thread'、'process' 或 'hybrid'
        self.parallel_mode = config_manager.get_str('keyword_matching.parallel_mode', 'hybrid')

        # 🚀 企业分片策略配置
        self.enterprise_sharding_config = config_manager.get_dict('keyword_matching.enterprise_sharding', {})
        self.enable_enterprise_sharding = self.enterprise_sharding_config.get('enable_enterprise_sharding', True)
        self.sharding_threshold = self.enterprise_sharding_config.get('sharding_threshold', 1000000)
        self.min_enterprise_batch_size = self.enterprise_sharding_config.get('min_enterprise_batch_size', 100)
        self.max_enterprise_batch_size = self.enterprise_sharding_config.get('max_enterprise_batch_size', 1000)
        self.shared_memory_threshold = self.enterprise_sharding_config.get('shared_memory_threshold', 1000)
        self.enable_shared_memory = self.enterprise_sharding_config.get('enable_shared_memory', True)

        # 🚀 内存换效率优化：预计算数据缓存
        self._precomputed_keyword_dicts = None
        self._precomputed_input_dicts = None
        self._data_fingerprint = None  # 用于检测数据变化

        # 获取文件路径配置
        self.keyword_file_path = config_manager.get_str('keyword_matching.keyword_file_path')
        self.keyword_sheet_name = config_manager.get_str('keyword_matching.keyword_sheet_name')
        self.input_file_path = config_manager.get_str('keyword_matching.input_file_path')
        self.input_sheet_name = config_manager.get_str('keyword_matching.input_sheet_name')

        # 从配置获取结果字段名称
        self.keyword_identifier_field = config_manager.get_str('keyword_matching.result_fields.keyword_identifier.field_name', 'keyword_index')
        self.company_identifier_field = config_manager.get_str('keyword_matching.result_fields.company_identifier.field_name', 'company_id')

        # 获取parquet数据源配置
        self.parquet_config = config_manager.get_dict('keyword_matching.parquet_data_source', {})
        self.folder_path_1 = self.parquet_config.get('folder_path_1', '')
        self.folder_path_2 = self.parquet_config.get('folder_path_2', '')
        self.merge_columns = self.parquet_config.get('merge_on_columns', ['lc_company_id', 'company_name'])

        # 验证parquet配置
        self._validate_parquet_config()

        # 进度跟踪
        self.progress = MatchingProgress()
        self.progress_bar: Optional[ProgressBar] = None

        # 🚀 内存映射优化：共享内存块管理
        self._company_data_block = None
        self._keyword_data_block = None

        self.logger.info("关键词匹配器初始化完成")

    def cleanup_shared_memory(self):
        """🚀 内存映射优化：清理共享内存资源"""
        try:
            if hasattr(self, '_company_data_block') and self._company_data_block:
                memory_manager = get_shared_memory_manager()
                memory_manager.cleanup_block(self._company_data_block)
                self._company_data_block = None
                self.logger.info("清理企业数据共享内存块")

            if hasattr(self, '_keyword_data_block') and self._keyword_data_block:
                memory_manager = get_shared_memory_manager()
                memory_manager.cleanup_block(self._keyword_data_block)
                self._keyword_data_block = None
                self.logger.info("清理关键词数据共享内存块")

        except Exception as e:
            self.logger.warning(f"清理共享内存时发生错误: {e}")

    def _validate_parquet_config(self):
        """验证parquet配置的有效性"""
        errors = []
        warnings = []

        # 检查文件夹路径
        if not self.folder_path_1:
            errors.append("Parquet文件夹路径1未配置")
        elif not Path(self.folder_path_1).exists():
            warnings.append(f"Parquet文件夹1不存在: {self.folder_path_1}")

        if not self.folder_path_2:
            errors.append("Parquet文件夹路径2未配置")
        elif not Path(self.folder_path_2).exists():
            warnings.append(f"Parquet文件夹2不存在: {self.folder_path_2}")

        # 检查合并列配置
        if not self.merge_columns:
            errors.append("合并列配置为空")
        elif not isinstance(self.merge_columns, list):
            errors.append("合并列配置必须是列表格式")

        # 记录验证结果
        if errors:
            error_msg = "Parquet配置验证失败: " + "; ".join(errors)
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        if warnings:
            for warning in warnings:
                self.logger.warning(f"Parquet配置警告: {warning}")

        if not errors:
            self.logger.info("Parquet配置验证通过")

    def _calculate_progress_update_interval(self) -> float:
        """
        根据数据量动态计算进度条更新间隔

        Returns:
            float: 更新间隔（秒）
        """
        total_combinations = self.progress.total_combinations

        if total_combinations <= 10000:
            # 小数据量：更频繁更新
            return 0.1
        elif total_combinations <= 100000:
            # 中等数据量：标准更新
            return 0.5
        elif total_combinations <= 1000000:
            # 大数据量：较少更新
            return 1.0
        else:
            # 超大数据量：最少更新
            return 2.0

    def _get_progress_update_frequency(self) -> int:
        """
        获取进度条更新频率（每多少次处理更新一次）

        Returns:
            int: 更新频率
        """
        total_combinations = self.progress.total_combinations

        if total_combinations <= 1000:
            # 小数据量：每次都更新
            return 1
        elif total_combinations <= 10000:
            # 小中数据量：每10次更新
            return 10
        elif total_combinations <= 100000:
            # 中等数据量：每100次更新
            return 100
        elif total_combinations <= 1000000:
            # 大数据量：每1000次更新
            return 1000
        else:
            # 超大数据量：每5000次更新
            return 5000
    
    def load_keyword_data(self, file_path: Optional[str] = None, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        加载关键词数据表格
        
        Args:
            file_path: 关键词文件路径（可选）
            sheet_name: 工作表名称（可选）
            
        Returns:
            pd.DataFrame: 关键词数据表格
        """
        # 使用参数或配置中的路径
        file_path = file_path or self.keyword_file_path
        sheet_name = sheet_name or self.keyword_sheet_name
        
        # 自动检测文件路径
        if file_path == "auto_detect":
            file_path = self._auto_detect_keyword_file()
        
        if not file_path or not Path(file_path).exists():
            raise FileNotFoundError(f"关键词文件不存在: {file_path}")
        
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            self.logger.info(f"成功加载关键词数据: {len(df)} 行, 文件: {file_path}")
            return df
        except Exception as e:
            self.logger.error(f"加载关键词数据失败: {e}")
            raise

    async def load_keyword_data_async(self, file_path: Optional[str] = None, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        🚀 异步处理优化：异步加载关键词数据表格

        Args:
            file_path: 关键词文件路径（可选）
            sheet_name: 工作表名称（可选）

        Returns:
            pd.DataFrame: 关键词数据表格
        """
        # 使用参数或配置中的路径
        file_path = file_path or self.keyword_file_path
        sheet_name = sheet_name or self.keyword_sheet_name

        # 自动检测文件路径
        if file_path == "auto_detect":
            file_path = self._auto_detect_keyword_file()

        if not file_path or not Path(file_path).exists():
            raise FileNotFoundError(f"关键词文件不存在: {file_path}")

        try:
            async_io_manager = get_async_io_manager()
            df = await async_io_manager.read_excel_file_async(file_path, sheet_name)

            if df is None:
                raise Exception("异步读取返回空数据")

            self.logger.info(f"🚀 异步加载关键词数据: {len(df)} 行, 文件: {file_path}")
            return df
        except Exception as e:
            self.logger.error(f"异步加载关键词数据失败: {e}")
            raise

    def load_input_data(self, file_path: str, sheet_name: Optional[str] = None) -> pd.DataFrame:
        """
        加载输入数据表格

        Args:
            file_path: 输入文件路径或parquet文件名
            sheet_name: 工作表名称（可选）

        Returns:
            pd.DataFrame: 输入数据表格
        """
        sheet_name = sheet_name or self.input_sheet_name

        # 检查是否为parquet文件名（不含路径分隔符）
        if '/' not in file_path and '\\' not in file_path and file_path.endswith('.parquet'):
            # 使用parquet文件夹模式
            return self.load_parquet_data_from_folders(file_path)

        # 传统文件路径模式
        if not Path(file_path).exists():
            raise FileNotFoundError(f"输入文件不存在: {file_path}")

        try:
            # 根据文件扩展名选择读取方法
            file_ext = Path(file_path).suffix.lower()
            if file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
            elif file_ext == '.csv':
                df = pd.read_csv(file_path)
            elif file_ext == '.parquet':
                df = pd.read_parquet(file_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            self.logger.info(f"成功加载输入数据: {len(df)} 行, 文件: {file_path}")
            return df
        except Exception as e:
            self.logger.error(f"加载输入数据失败: {e}")
            raise

    def load_parquet_data_from_folders(self, file_name: str) -> pd.DataFrame:
        """
        从两个parquet文件夹中读取同名文件并合并

        Args:
            file_name: parquet文件名（不含路径）

        Returns:
            pd.DataFrame: 合并后的数据表格
        """


        if not self.folder_path_1 or not self.folder_path_2:
            raise ValueError("Parquet文件夹路径未配置")

        # 构建完整文件路径
        file_path_1 = Path(self.folder_path_1) / file_name
        file_path_2 = Path(self.folder_path_2) / file_name

        # 检查文件是否存在
        if not file_path_1.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_1}")
        if not file_path_2.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_2}")

        try:
            # 读取两个parquet文件
            self.logger.info(f"读取parquet文件: {file_path_1}")
            df1 = pd.read_parquet(file_path_1)

            self.logger.info(f"读取parquet文件: {file_path_2}")
            df2 = pd.read_parquet(file_path_2)

            # 验证合并列是否存在
            for col in self.merge_columns:
                if col not in df1.columns:
                    raise ValueError(f"合并列 '{col}' 在文件1中不存在: {file_path_1}")
                if col not in df2.columns:
                    raise ValueError(f"合并列 '{col}' 在文件2中不存在: {file_path_2}")

            # 合并数据
            self.logger.info(f"合并数据，基于列: {self.merge_columns}")
            merged_df = pd.merge(df1, df2, on=self.merge_columns, how='inner')

            self.logger.info(f"成功合并parquet数据: 文件1 {len(df1)} 行, 文件2 {len(df2)} 行, 合并后 {len(merged_df)} 行")
            return merged_df

        except Exception as e:
            self.logger.error(f"加载和合并parquet数据失败: {e}")
            raise

    async def load_and_merge_parquet_data_async(self, file_name: str) -> pd.DataFrame:
        """
        🚀 异步处理优化：异步加载并合并两个parquet文件的数据

        Args:
            file_name: parquet文件名（不含路径）

        Returns:
            pd.DataFrame: 合并后的数据
        """


        if not self.folder_path_1 or not self.folder_path_2:
            raise ValueError("Parquet文件夹路径未配置")

        # 构建完整文件路径
        file_path_1 = Path(self.folder_path_1) / file_name
        file_path_2 = Path(self.folder_path_2) / file_name

        # 检查文件是否存在
        if not file_path_1.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_1}")
        if not file_path_2.exists():
            raise FileNotFoundError(f"文件不存在: {file_path_2}")

        try:
            # 🚀 异步处理优化：并发读取两个parquet文件
            async_io_manager = get_async_io_manager()

            self.logger.info(f"🚀 异步读取parquet文件: {file_path_1} 和 {file_path_2}")

            # 并发读取两个文件
            df1_task = async_io_manager.read_parquet_file_async(str(file_path_1))
            df2_task = async_io_manager.read_parquet_file_async(str(file_path_2))

            df1, df2 = await asyncio.gather(df1_task, df2_task)

            if df1 is None or df2 is None:
                raise Exception("异步读取parquet文件失败")

            # 验证合并列是否存在
            for col in self.merge_columns:
                if col not in df1.columns:
                    raise ValueError(f"合并列 '{col}' 在文件1中不存在: {file_path_1}")
                if col not in df2.columns:
                    raise ValueError(f"合并列 '{col}' 在文件2中不存在: {file_path_2}")

            # 合并数据
            self.logger.info(f"合并数据，基于列: {self.merge_columns}")
            merged_df = pd.merge(df1, df2, on=self.merge_columns, how='inner')

            self.logger.info(f"🚀 异步合并parquet数据: 文件1 {len(df1)} 行, 文件2 {len(df2)} 行, 合并后 {len(merged_df)} 行")
            return merged_df

        except Exception as e:
            self.logger.error(f"异步加载和合并parquet数据失败: {e}")
            raise

    def get_available_parquet_files(self) -> List[str]:
        """
        获取两个文件夹中的同名parquet文件列表

        Returns:
            List[str]: 同名parquet文件名列表
        """


        if not self.folder_path_1 or not self.folder_path_2:
            return []

        try:
            folder1 = Path(self.folder_path_1)
            folder2 = Path(self.folder_path_2)

            if not folder1.exists() or not folder2.exists():
                self.logger.warning(f"Parquet文件夹不存在: {folder1} 或 {folder2}")
                return []

            # 获取两个文件夹中的parquet文件
            files1 = set(f.name for f in folder1.glob('*.parquet'))
            files2 = set(f.name for f in folder2.glob('*.parquet'))

            # 返回同名文件
            common_files = list(files1.intersection(files2))
            common_files.sort()

            self.logger.info(f"找到 {len(common_files)} 个同名parquet文件")
            return common_files

        except Exception as e:
            self.logger.error(f"获取parquet文件列表失败: {e}")
            return []

    def get_random_parquet_file(self) -> Optional[str]:
        """
        随机选择一个parquet文件用于测试

        Returns:
            Optional[str]: 随机选择的文件名，如果没有文件则返回None
        """
        available_files = self.get_available_parquet_files()
        if not available_files:
            return None

        selected_file = random.choice(available_files)
        self.logger.info(f"随机选择测试文件: {selected_file}")
        return selected_file

    def run_batch_processing(self, keyword_file_path: Optional[str] = None,
                           output_base_path: Optional[str] = None) -> List[Tuple[str, str, str]]:
        """
        批量处理所有parquet文件

        Args:
            keyword_file_path: 关键词文件路径（可选）
            output_base_path: 输出文件基础路径（可选）

        Returns:
            List[Tuple[str, str, str]]: (文件名, 结果文件路径, 报告文件路径) 的列表
        """


        available_files = self.get_available_parquet_files()
        if not available_files:
            raise ValueError("没有找到可处理的parquet文件")

        batch_config = self.parquet_config.get('batch_processing', {})
        continue_on_error = batch_config.get('continue_on_error', True)
        max_errors = batch_config.get('max_errors', 50)
        progress_interval = batch_config.get('progress_report_interval', 10)

        results = []
        error_count = 0

        self.logger.info(f"开始批量处理 {len(available_files)} 个parquet文件")

        for i, file_name in enumerate(available_files, 1):
            try:
                self.logger.info(f"处理文件 {i}/{len(available_files)}: {file_name}")

                # 生成输出文件路径
                base_name = Path(file_name).stem
                if output_base_path:
                    output_file = f"{output_base_path}_{base_name}.xlsx"
                else:
                    output_file = f"batch_results_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

                # 执行单个文件的匹配
                result_file, report_file = self.run_complete_matching(
                    input_file_path=file_name,  # 传递文件名，load_input_data会识别为parquet模式
                    keyword_file_path=keyword_file_path,
                    output_file_path=output_file
                )

                results.append((file_name, result_file, report_file))

                # 定期报告进度
                if i % progress_interval == 0:
                    self.logger.info(f"批处理进度: {i}/{len(available_files)} 完成")

            except Exception as e:
                error_count += 1
                self.logger.error(f"处理文件 {file_name} 失败: {e}")

                if not continue_on_error or error_count >= max_errors:
                    self.logger.error(f"错误过多或设置不继续处理，停止批处理")
                    break

        self.logger.info(f"批处理完成: 成功 {len(results)} 个文件, 失败 {error_count} 个文件")
        return results

    def run_random_test(self, keyword_file_path: Optional[str] = None,
                       output_file_path: Optional[str] = None) -> Tuple[str, str, str]:
        """
        随机选择一个文件进行快速测试

        Args:
            keyword_file_path: 关键词文件路径（可选）
            output_file_path: 输出文件路径（可选）

        Returns:
            Tuple[str, str, str]: (测试文件名, 结果文件路径, 报告文件路径)
        """


        test_file = self.get_random_parquet_file()
        if not test_file:
            raise ValueError("没有找到可测试的parquet文件")

        # 生成输出文件路径
        if not output_file_path:
            base_name = Path(test_file).stem
            output_file_path = f"random_test_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"

        self.logger.info(f"开始随机测试，文件: {test_file}")

        # 执行匹配
        result_file, report_file = self.run_complete_matching(
            input_file_path=test_file,  # 传递文件名，load_input_data会识别为parquet模式
            keyword_file_path=keyword_file_path,
            output_file_path=output_file_path
        )

        self.logger.info(f"随机测试完成: {test_file}")
        return test_file, result_file, report_file

    def execute_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        执行关键词匹配

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        start_time = datetime.now()

        # 检查是否启用性能分析模式
        enable_profiling = self._should_enable_profiling()
        profiler = None

        if enable_profiling:
            profiler = PerformanceProfiler()
            profiler.start_timing("total_execution")
            profiler.start_timing("initialization")
            self.logger.info("🚀 启动性能监控，记录优化前后对比")

        # 初始化进度
        self.progress.total_input_rows = len(input_df)
        self.progress.total_combinations = len(keyword_df) * len(input_df)
        self.progress.processed_combinations = 0
        self.progress.successful_matches = 0
        self.progress.failed_matches = 0

        self.logger.info(f"开始执行匹配: {len(keyword_df)} 个关键词规则 × {len(input_df)} 条输入数据")

        # 启动进度条（根据数据量动态调整更新间隔）
        update_interval = self._calculate_progress_update_interval()
        self.progress_bar = create_progress_bar(
            total=self.progress.total_combinations,
            description="关键词匹配",
            bar_length=20,
            update_interval=update_interval
        )
        self.progress_bar.start()

        if enable_profiling:
            profiler.end_timing("initialization")
            profiler.start_timing("matching_execution")

        matching_results = []

        try:
            if self.enable_optimization and self.max_workers > 1:
                # 并行处理
                matching_results = self._execute_parallel_matching(keyword_df, input_df)
            else:
                # 串行处理
                matching_results = self._execute_serial_matching(keyword_df, input_df)

            if enable_profiling:
                profiler.end_timing("matching_execution")
                profiler.start_timing("finalization")

            end_time = datetime.now()
            processing_time = (end_time - start_time).total_seconds()

            # 完成进度条显示
            if self.progress_bar:
                self.progress_bar.finish(f"匹配完成! 成功: {self.progress.successful_matches}, 失败: {self.progress.failed_matches}")
                self.progress_bar = None

            self.logger.info(f"匹配完成: 处理时间 {processing_time:.2f}s, "
                           f"成功匹配 {self.progress.successful_matches} 条, "
                           f"失败 {self.progress.failed_matches} 条")

            # 🚀 优化：记录性能对比数据
            combinations_per_second = self.progress.total_combinations / processing_time if processing_time > 0 else 0
            self.logger.info(f"🚀 性能统计: {combinations_per_second:.0f} 组合/秒, "
                           f"并行模式: {self.parallel_mode}, "
                           f"工作进程: {self.max_workers}")

            # 记录缓存性能
            if hasattr(self.matching_engine.compiler, 'performance_stats'):
                cache_stats = self.matching_engine.compiler.get_performance_stats()
                cache_hit_rate = (cache_stats.get('cache_hits', 0) /
                                max(cache_stats.get('cache_hits', 0) + cache_stats.get('cache_misses', 0), 1)) * 100
                self.logger.info(f"🚀 缓存性能: 命中率 {cache_hit_rate:.1f}%, "
                               f"共享缓存命中 {cache_stats.get('shared_cache_hits', 0)} 次")

            if enable_profiling:
                profiler.end_timing("finalization")
                profiler.end_timing("total_execution")
                self._output_performance_analysis(profiler, len(keyword_df), len(input_df))

            return matching_results

        except Exception as e:
            # 确保进度条在异常时也能正确关闭
            if self.progress_bar:
                self.progress_bar.finish("匹配过程中发生错误")
                self.progress_bar = None
            self.logger.error(f"执行匹配时发生错误: {e}")
            raise

    def _should_enable_profiling(self) -> bool:
        """判断是否应该启用性能分析"""
        # 通过环境变量或配置参数控制
        import os
        return (
            os.getenv('KEYWORD_MATCHER_PROFILING', '').lower() in ['true', '1', 'yes'] or
            getattr(self, 'enable_profiling', False)
        )

    def _output_performance_analysis(self, profiler: PerformanceProfiler, keyword_count: int, input_count: int):
        """输出性能分析结果"""
        total_combinations = keyword_count * input_count

        print("\n" + "=" * 80)
        print("🔍 深度性能分析报告 - execute_matching 方法")
        print("=" * 80)

        # 总体性能
        total_time = profiler.get_total_time()
        print(f"\n📊 总体性能指标:")
        print(f"  总执行时间: {total_time:.3f} 秒")
        print(f"  总组合数量: {total_combinations:,}")
        print(f"  平均处理速度: {total_combinations/total_time:.0f} 组合/秒" if total_time > 0 else "  平均处理速度: N/A")

        # 各步骤详细分析
        print(f"\n⏱️ 各步骤耗时详细分析:")
        step_order = ['initialization', 'matching_execution', 'finalization']

        for step in step_order:
            duration = profiler.get_step_duration(step)
            if duration > 0:
                percentage = (duration / total_time * 100) if total_time > 0 else 0
                memory_growth = profiler.get_memory_growth(step)

                print(f"  {step}:")
                print(f"    耗时: {duration:.3f} 秒 ({percentage:.1f}%)")
                print(f"    内存增长: {memory_growth:.1f} MB")

                if step == 'matching_execution':
                    speed = total_combinations / duration if duration > 0 else 0
                    print(f"    匹配速度: {speed:.0f} 组合/秒")

        # 性能计数器
        if profiler.counters:
            print(f"\n📈 性能计数器:")
            for counter_name, count in profiler.counters.items():
                print(f"  {counter_name}: {count:,}")

        # 瓶颈分析
        print(f"\n🎯 性能瓶颈分析:")
        matching_duration = profiler.get_step_duration('matching_execution')
        matching_percentage = (matching_duration / total_time * 100) if total_time > 0 else 0

        if matching_percentage > 90:
            print(f"  ⚠️ 匹配执行阶段占用 {matching_percentage:.1f}% 的时间，是主要瓶颈")
            print(f"  🔧 建议优化方向:")
            print(f"    1. 优化正则表达式编译和缓存策略")
            print(f"    2. 减少 DataFrame.to_dict() 调用次数")
            print(f"    3. 优化文本预处理和清理逻辑")
            print(f"    4. 增加并行处理的工作进程数量")
        else:
            print(f"  ✅ 各步骤耗时相对均衡，无明显瓶颈")

        # 内存使用分析
        max_memory = max([profiler.get_memory_growth(step) for step in step_order if profiler.get_step_duration(step) > 0], default=0)
        if max_memory > 500:  # 500MB
            print(f"  ⚠️ 内存使用较高 ({max_memory:.1f}MB)，建议优化内存管理")

        print("=" * 80)

    def _execute_serial_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """串行执行匹配 - 优化版本"""
        matching_results = []

        # 🚀 优化1：预先转换为字典，避免重复调用to_dict()
        self.logger.info("开始预处理数据结构（串行模式）...")
        start_time = time.time()

        keyword_dicts = [row.to_dict() for _, row in keyword_df.iterrows()]
        input_dicts = [row.to_dict() for _, row in input_df.iterrows()]

        preprocess_time = time.time() - start_time
        self.logger.info(f"预处理完成: {len(keyword_dicts)} 个关键词, {len(input_dicts)} 个输入数据, 耗时: {preprocess_time:.3f}秒")

        # 使用预处理的字典进行匹配
        for input_idx, input_dict in enumerate(input_dicts):
            self.progress.current_input_row = input_idx + 1

            for keyword_idx, keyword_dict in enumerate(keyword_dicts):
                result = self._process_single_combination_optimized(keyword_idx, keyword_dict, input_dict)
                if result:
                    matching_results.append(result)

                self.progress.processed_combinations += 1

                # 根据数据量动态调整进度条更新频率
                update_frequency = self._get_progress_update_frequency()
                if self.progress_bar and self.progress.processed_combinations % update_frequency == 0:
                    self.progress_bar.update(self.progress.processed_combinations)

                # 定期输出进度日志（降低频率以避免影响性能）
                if self.progress.processed_combinations % (update_frequency * 10) == 0:
                    self._log_progress()

        return matching_results

    def _process_single_combination_optimized(self, keyword_idx: int, keyword_dict: Dict[str, Any], input_dict: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        处理单个组合 - 深度优化版本

        Args:
            keyword_idx: 关键词索引
            keyword_dict: 关键词字典（已预处理）
            input_dict: 输入数据字典（已预处理）

        Returns:
            Optional[Dict[str, Any]]: 匹配结果，如果匹配失败返回None
        """
        try:
            # 🚀 优化1：直接使用预处理的字典，避免重复to_dict()调用

            # 🚀 优化2：快速筛选逻辑预检查
            if not self._fast_filter_precheck(keyword_dict, input_dict):
                self.progress.failed_matches += 1
                return None

            # 应用完整筛选逻辑
            filter_result = self.filter_logic.apply_combined_filter(keyword_dict, input_dict)
            if not filter_result.passed:
                self.progress.failed_matches += 1
                return None

            # 🚀 优化3：使用优化的匹配引擎
            match_result = self.matching_engine.match_keywords(
                keyword_dict, input_dict, filter_result.filtered_columns
            )

            if match_result.success:
                self.progress.successful_matches += 1

                # 构建结果记录
                result_record = {
                    'keyword_index': keyword_idx,
                    'input_index': input_dict.get('index', 0),
                    'keyword_data': keyword_dict,
                    'input_data': input_dict,
                    'match_result': match_result,
                    'match_timestamp': datetime.now().isoformat()
                }

                return result_record
            else:
                self.progress.failed_matches += 1
                return None

        except Exception as e:
            self.logger.error(f"处理组合时发生错误 (keyword_idx={keyword_idx}): {e}")
            self.progress.failed_matches += 1
            return None

    def _fast_filter_precheck(self, keyword_dict: Dict[str, Any], input_dict: Dict[str, Any]) -> bool:
        """
        快速筛选预检查 - 在完整筛选逻辑之前进行快速判断

        Args:
            keyword_dict: 关键词字典
            input_dict: 输入数据字典

        Returns:
            bool: True表示可能通过筛选，False表示肯定不通过
        """
        try:
            # 🚀 优化：检查关键的筛选条件，快速排除明显不匹配的组合

            # 检查注册资本范围（如果有的话）
            min_capital = keyword_dict.get('min_registered_capital')
            max_capital = keyword_dict.get('max_registered_capital')
            input_capital = input_dict.get('registered_capital')

            if min_capital is not None and input_capital is not None:
                try:
                    min_val = float(min_capital) if min_capital != '' else 0
                    max_val = float(max_capital) if max_capital != '' else float('inf')
                    input_val = float(input_capital) if input_capital != '' else 0

                    if not (min_val <= input_val <= max_val):
                        return False
                except (ValueError, TypeError):
                    pass

            # 检查关键词是否为空（快速排除无效规则）
            like_keyword = keyword_dict.get('converted_like_keyword', '').strip()
            must_keyword = keyword_dict.get('converted_must_keyword', '').strip()
            unlike_keyword = keyword_dict.get('converted_unlike_keyword', '').strip()

            # 如果所有关键词都为空或默认值，可能是无效规则
            if (not like_keyword or like_keyword == '0') and \
               (not must_keyword or must_keyword == '0') and \
               (not unlike_keyword or unlike_keyword == '0'):
                return False

            return True

        except Exception:
            # 预检查失败时，保守地返回True，让完整筛选逻辑处理
            return True

    def _execute_parallel_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        并行执行匹配 - 优化版本

        使用基于索引的轻量级策略，避免大规模数据拷贝
        """
        # 生成所有组合
        total_combinations = len(keyword_df) * len(input_df)

        # 智能选择并行模式和批处理大小
        optimal_parallel_mode, optimal_batch_size = self._calculate_optimal_parallel_config(total_combinations)
        actual_batch_size = min(self.batch_size, optimal_batch_size)

        # 如果智能模式与配置不同，给出建议
        if optimal_parallel_mode != self.parallel_mode:
            self.logger.info(f"建议并行模式: {optimal_parallel_mode} (当前: {self.parallel_mode})")

        self.logger.info(f"总组合数: {total_combinations}, 配置批处理大小: {self.batch_size}, 优化批处理大小: {actual_batch_size}, 工作进程/线程: {self.max_workers}")

        # 🚀 优化：智能选择处理策略，支持混合模式
        memory_estimate = self._estimate_memory_usage(keyword_df, input_df, total_combinations)
        strategy, actual_parallel_mode = self._select_optimal_strategy_with_mode(total_combinations, memory_estimate)

        self.logger.info(f"选择处理策略: {strategy}, 并行模式: {actual_parallel_mode}, 预估内存: {memory_estimate/1024/1024:.1f}MB")

        if strategy == 'chunked':
            return self._execute_chunked_parallel_matching(keyword_df, input_df, actual_batch_size)
        elif strategy == 'serial':
            self.logger.info("数据量较小，切换到串行处理以获得更好性能")
            return self._execute_serial_matching(keyword_df, input_df)

        # 🚀 优化：动态调整并行模式
        self.parallel_mode = actual_parallel_mode

        # 🚀 内存换效率优化：智能预计算数据结构
        keyword_dicts, input_dicts = self._get_or_compute_data_dicts(keyword_df, input_df)

        # 🚀 新增：混合模式处理
        if actual_parallel_mode == 'hybrid':
            return self._execute_hybrid_parallel_matching(keyword_df, input_df, keyword_dicts, input_dicts, actual_batch_size)

        # 🚀 数据分片优化：选择最优的分片策略
        if (self.enable_optimization and self.enable_enterprise_sharding and
            total_combinations > self.sharding_threshold):  # 大数据集使用企业分片
            return self._execute_enterprise_sharded_matching(
                keyword_df, input_df, keyword_dicts, input_dicts, actual_batch_size
            )
        else:
            # 使用流式批次处理 - 避免一次性创建所有批次
            return self._execute_streaming_parallel_matching(
                keyword_df, input_df, keyword_dicts, input_dicts, actual_batch_size
            )

    def _execute_streaming_parallel_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame,
                                           keyword_dicts: List[Dict], input_dicts: List[Dict],
                                           batch_size: int) -> List[Dict[str, Any]]:
        """
        流式并行匹配 - 避免一次性创建所有批次，提高响应性

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            keyword_dicts: 预处理的关键词字典列表
            input_dicts: 预处理的输入数据字典列表
            batch_size: 批处理大小

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        matching_results = []

        # 选择执行器类型
        executor_class = ProcessPoolExecutor if self.parallel_mode == 'process' else ThreadPoolExecutor

        with executor_class(max_workers=self.max_workers) as executor:
            # 使用滑动窗口提交任务，避免一次性创建所有批次
            future_to_batch = {}
            batch_idx = 0
            completed_batches = 0

            # 批次生成器
            def batch_generator():
                """生成批次的生成器"""
                batch_combinations = []
                for input_idx in range(len(input_df)):
                    for keyword_idx in range(len(keyword_df)):
                        batch_combinations.append((keyword_idx, input_idx))

                        if len(batch_combinations) >= batch_size:
                            yield batch_combinations
                            batch_combinations = []

                if batch_combinations:
                    yield batch_combinations

            # 🚀 流式提交和处理 - 优化并发控制
            batch_gen = batch_generator()
            # 🚀 优化：减少待处理批次数量，降低内存压力和通信开销
            max_pending_batches = min(self.max_workers * 2, 16)  # 按要求从4倍降低到2倍

            # 初始提交一批任务
            for _ in range(max_pending_batches):
                try:
                    batch = next(batch_gen)
                    if self.parallel_mode == 'process':
                        future = executor.submit(process_optimized_batch_worker, batch, keyword_dicts, input_dicts)
                    else:
                        future = executor.submit(self._process_optimized_batch, batch, keyword_dicts, input_dicts)

                    future_to_batch[future] = (batch_idx, len(batch))
                    batch_idx += 1
                except StopIteration:
                    break

            # 处理完成的任务并提交新任务
            while future_to_batch:
                # 等待至少一个任务完成
                for future in as_completed(future_to_batch):
                    batch_info = future_to_batch.pop(future)
                    current_batch_idx, batch_size_actual = batch_info

                    try:
                        batch_results = future.result()
                        matching_results.extend(batch_results)
                        completed_batches += 1

                        # 更新进度统计 - 正确统计成功和失败的匹配
                        self.progress.processed_combinations += batch_size_actual
                        successful_in_batch = sum(1 for r in batch_results if r.get('match_success', False))
                        failed_in_batch = len(batch_results) - successful_in_batch
                        self.progress.successful_matches += successful_in_batch
                        self.progress.failed_matches += failed_in_batch

                        # 更新进度条
                        if self.progress_bar:
                            self.progress_bar.update(self.progress.processed_combinations)

                        self.logger.debug(f"批次 {current_batch_idx} 处理完成，结果数: {len(batch_results)}")

                    except Exception as e:
                        self.logger.error(f"批次 {current_batch_idx} 处理失败: {e}")
                        completed_batches += 1

                    # 提交新任务（如果还有）
                    if len(future_to_batch) < max_pending_batches:
                        try:
                            batch = next(batch_gen)
                            if self.parallel_mode == 'process':
                                new_future = executor.submit(process_optimized_batch_worker, batch, keyword_dicts, input_dicts)
                            else:
                                new_future = executor.submit(self._process_optimized_batch, batch, keyword_dicts, input_dicts)

                            future_to_batch[new_future] = (batch_idx, len(batch))
                            batch_idx += 1
                        except StopIteration:
                            pass  # 没有更多批次了

                    break  # 只处理一个完成的任务，然后继续循环

        self.logger.info(f"流式并行处理完成: 处理了 {completed_batches} 个批次")
        return matching_results

    def _execute_chunked_parallel_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame, batch_size: int) -> List[Dict[str, Any]]:
        """
        分块并行匹配 - 用于超大数据集

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            batch_size: 批处理大小

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        matching_results = []

        # 计算合适的分块大小
        chunk_size = min(10000, len(input_df) // 4)  # 每块最多1万行输入数据
        if chunk_size < 1000:
            chunk_size = len(input_df)  # 如果数据量不大，不分块

        self.logger.info(f"使用分块策略: 每块 {chunk_size} 行输入数据")

        # 预处理关键词数据（一次性转换）
        keyword_dicts = [row.to_dict() for _, row in keyword_df.iterrows()]

        # 分块处理输入数据
        for chunk_start in range(0, len(input_df), chunk_size):
            chunk_end = min(chunk_start + chunk_size, len(input_df))
            input_chunk = input_df.iloc[chunk_start:chunk_end]

            self.logger.info(f"处理数据块: {chunk_start+1}-{chunk_end} / {len(input_df)}")

            # 为当前块预处理输入数据
            input_chunk_dicts = [row.to_dict() for _, row in input_chunk.iterrows()]

            # 使用流式处理当前块，避免批次预创建阻塞
            chunk_results = self._process_chunk_streaming(keyword_dicts, input_chunk_dicts, batch_size)
            matching_results.extend(chunk_results)

            # 更新总体进度
            processed_in_chunk = len(input_chunk_dicts) * len(keyword_dicts)
            self.progress.processed_combinations += processed_in_chunk

        return matching_results

    def _execute_enterprise_sharded_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame,
                                           keyword_dicts: List[Dict], input_dicts: List[Dict],
                                           batch_size: int) -> List[Dict[str, Any]]:
        """
        🚀 数据分片优化：按企业数据分片的并行匹配

        每个进程处理完整的企业数据子集，避免重复数据传输和序列化开销

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            keyword_dicts: 关键词字典列表
            input_dicts: 输入数据字典列表
            batch_size: 批处理大小（这里指企业数据的批次大小）

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        matching_results = []
        total_combinations = len(keyword_dicts) * len(input_dicts)

        self.logger.info(f"🚀 启动企业分片并行处理: {len(input_dicts)} 个企业, {len(keyword_dicts)} 个关键词")

        # 🚀 关键优化：按企业数据分片，而不是按组合分片
        enterprise_batch_size = max(
            self.min_enterprise_batch_size,
            min(batch_size // len(keyword_dicts), self.max_enterprise_batch_size)
        )  # 每批次企业数量
        enterprise_batches = [
            input_dicts[i:i + enterprise_batch_size]
            for i in range(0, len(input_dicts), enterprise_batch_size)
        ]

        self.logger.info(f"🚀 企业分片策略: {len(enterprise_batches)} 个批次, 每批次 ~{enterprise_batch_size} 个企业")

        # 🚀 优化：使用进程池处理企业批次
        executor_class = ProcessPoolExecutor if self.parallel_mode == 'process' else ThreadPoolExecutor

        with executor_class(max_workers=self.max_workers) as executor:
            # 🚀 关键优化：每个进程处理一个企业批次，关键词数据在所有进程间共享
            future_to_batch = {
                executor.submit(process_enterprise_batch_worker, enterprise_batch, keyword_dicts): batch_idx
                for batch_idx, enterprise_batch in enumerate(enterprise_batches)
            }

            completed_batches = 0
            for future in as_completed(future_to_batch):
                batch_idx = future_to_batch[future]
                try:
                    batch_results = future.result()
                    matching_results.extend(batch_results)

                    completed_batches += 1
                    processed_enterprises = min(completed_batches * enterprise_batch_size, len(input_dicts))
                    processed_combinations = processed_enterprises * len(keyword_dicts)

                    # 更新进度 - 正确统计成功和失败的匹配
                    self.progress.processed_combinations = processed_combinations
                    successful_in_batch = sum(1 for r in batch_results if r.get('match_success', False))
                    failed_in_batch = len(batch_results) - successful_in_batch
                    self.progress.successful_matches += successful_in_batch
                    self.progress.failed_matches += failed_in_batch

                    self.logger.info(f"🚀 企业批次 {completed_batches}/{len(enterprise_batches)} 完成, "
                                   f"已处理 {processed_enterprises}/{len(input_dicts)} 个企业, "
                                   f"成功匹配 {len(batch_results)} 条")

                except Exception as e:
                    self.logger.error(f"企业批次 {batch_idx} 处理失败: {e}")

        self.logger.info(f"🚀 企业分片并行处理完成: 总匹配结果 {len(matching_results)} 条")
        return matching_results

    def _process_chunk_streaming(self, keyword_dicts: List[Dict], input_chunk_dicts: List[Dict], batch_size: int) -> List[Dict[str, Any]]:
        """
        流式处理数据块 - 避免批次预创建阻塞

        Args:
            keyword_dicts: 关键词字典列表
            input_chunk_dicts: 输入数据块字典列表
            batch_size: 批处理大小

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        chunk_results = []

        # 选择执行器类型
        executor_class = ProcessPoolExecutor if self.parallel_mode == 'process' else ThreadPoolExecutor

        with executor_class(max_workers=self.max_workers) as executor:
            future_to_batch = {}
            batch_idx = 0
            max_pending_batches = min(self.max_workers * 2, 10)  # 限制待处理批次数量

            # 批次生成器 - 为当前块生成批次
            def chunk_batch_generator():
                """为当前数据块生成批次的生成器"""
                batch_combinations = []
                for input_idx in range(len(input_chunk_dicts)):
                    for keyword_idx in range(len(keyword_dicts)):
                        batch_combinations.append((keyword_idx, input_idx))

                        if len(batch_combinations) >= batch_size:
                            yield batch_combinations
                            batch_combinations = []

                if batch_combinations:
                    yield batch_combinations

            # 流式提交和处理
            batch_gen = chunk_batch_generator()

            # 初始提交一批任务
            for _ in range(max_pending_batches):
                try:
                    batch = next(batch_gen)
                    if self.parallel_mode == 'process':
                        future = executor.submit(process_optimized_batch_worker, batch, keyword_dicts, input_chunk_dicts)
                    else:
                        future = executor.submit(self._process_optimized_batch, batch, keyword_dicts, input_chunk_dicts)

                    future_to_batch[future] = (batch_idx, len(batch))
                    batch_idx += 1
                except StopIteration:
                    break

            # 处理完成的任务并提交新任务
            while future_to_batch:
                for future in as_completed(future_to_batch):
                    batch_info = future_to_batch.pop(future)
                    current_batch_idx, batch_size_actual = batch_info

                    try:
                        batch_results = future.result()
                        chunk_results.extend(batch_results)

                        # 更新进度统计
                        self.progress.processed_combinations += batch_size_actual
                        self.progress.successful_matches += len(batch_results)
                        self.progress.failed_matches += batch_size_actual - len(batch_results)

                        # 更新进度条
                        if self.progress_bar:
                            self.progress_bar.update(self.progress.processed_combinations)

                        self.logger.debug(f"块内批次 {current_batch_idx} 处理完成，结果数: {len(batch_results)}")

                    except Exception as e:
                        self.logger.error(f"块内批次 {current_batch_idx} 处理失败: {e}")

                    # 提交新任务（如果还有）
                    if len(future_to_batch) < max_pending_batches:
                        try:
                            batch = next(batch_gen)
                            if self.parallel_mode == 'process':
                                new_future = executor.submit(process_optimized_batch_worker, batch, keyword_dicts, input_chunk_dicts)
                            else:
                                new_future = executor.submit(self._process_optimized_batch, batch, keyword_dicts, input_chunk_dicts)

                            future_to_batch[new_future] = (batch_idx, len(batch))
                            batch_idx += 1
                        except StopIteration:
                            pass  # 没有更多批次了

                    break  # 只处理一个完成的任务，然后继续循环

        return chunk_results

    def _process_chunk_parallel(self, chunk_combinations: List[List[Tuple]], keyword_dicts: List[Dict], input_dicts: List[Dict]) -> List[Dict[str, Any]]:
        """
        并行处理数据块

        Args:
            chunk_combinations: 块内的组合批次
            keyword_dicts: 关键词字典列表
            input_dicts: 输入数据字典列表

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        chunk_results = []

        executor_class = ProcessPoolExecutor if self.parallel_mode == 'process' else ThreadPoolExecutor

        with executor_class(max_workers=self.max_workers) as executor:
            if self.parallel_mode == 'process':
                future_to_batch = {
                    executor.submit(process_optimized_batch_worker, batch, keyword_dicts, input_dicts): batch_idx
                    for batch_idx, batch in enumerate(chunk_combinations)
                }
            else:
                future_to_batch = {
                    executor.submit(self._process_optimized_batch, batch, keyword_dicts, input_dicts): batch_idx
                    for batch_idx, batch in enumerate(chunk_combinations)
                }

            for future in as_completed(future_to_batch):
                try:
                    batch_results = future.result()
                    chunk_results.extend(batch_results)

                    # 更新进度条
                    if self.progress_bar:
                        self.progress_bar.update(self.progress.processed_combinations)

                except Exception as e:
                    batch_idx = future_to_batch[future]
                    self.logger.error(f"块内批次 {batch_idx} 处理失败: {e}")

        return chunk_results

    def _process_optimized_batch(self, index_batch: List[Tuple], keyword_dicts: List[Dict], input_dicts: List[Dict]) -> List[Dict[str, Any]]:
        """
        处理优化的索引批次 - 线程池版本

        Args:
            index_batch: 索引组合列表 [(keyword_idx, input_idx), ...]
            keyword_dicts: 关键词字典列表
            input_dicts: 输入数据字典列表

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        batch_results = []

        for keyword_idx, input_idx in index_batch:
            try:
                keyword_dict = keyword_dicts[keyword_idx]
                input_dict = input_dicts[input_idx]

                # 应用筛选逻辑
                filter_result = self.filter_logic.apply_combined_filter(keyword_dict, input_dict)
                if not filter_result.passed:
                    continue

                # 执行关键词匹配
                match_result = self.matching_engine.match_keywords(
                    keyword_dict, input_dict, filter_result.filtered_columns
                )

                if match_result.success:
                    # 构建结果记录（使用与原有逻辑一致的方式）
                    match_details = match_result.match_details or {}
                    stage_stats = match_details.get('stage_stats', {})

                    result = {
                        # 标识信息
                        'keyword_index': keyword_idx,
                        'lc_company_id': input_dict.get('lc_company_id', ''),
                        'company_name': input_dict.get('company_name', ''),

                        # 匹配结果信息
                        'match_success': True,
                        'matched_texts': match_result.matched_texts,
                        'match_details': match_details,

                        # 展开的统计信息字段
                        'total_texts_processed': match_details.get('total_texts_processed', 0),
                        'texts_passed_all_stages': match_details.get('texts_passed_all_stages', 0),
                        'like_passed_count': stage_stats.get('like_passed', 0),
                        'must_passed_count': stage_stats.get('must_passed', 0),
                        'unlike_passed_count': stage_stats.get('unlike_passed', 0),
                        'like_failed_count': stage_stats.get('like_failed', 0),
                        'must_failed_count': stage_stats.get('must_failed', 0),
                        'unlike_failed_count': stage_stats.get('unlike_failed', 0)
                    }
                    batch_results.append(result)

            except Exception as e:
                self.logger.error(f"处理组合失败 (keyword_idx={keyword_idx}, input_idx={input_idx}): {e}")

        return batch_results

    def _estimate_memory_usage(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame, total_combinations: int) -> int:
        """
        估算内存使用量

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            total_combinations: 总组合数

        Returns:
            int: 预估内存使用量（字节）
        """
        # 估算单行数据大小
        keyword_memory = keyword_df.memory_usage(deep=True).sum()
        input_memory = input_df.memory_usage(deep=True).sum()

        avg_keyword_size = keyword_memory / len(keyword_df) if len(keyword_df) > 0 else 1000
        avg_input_size = input_memory / len(input_df) if len(input_df) > 0 else 5000

        # 基础数据存储（一次性转换）
        base_memory = keyword_memory + input_memory

        # 索引组合存储（每个组合16字节：两个int64）
        index_memory = total_combinations * 16

        # 批处理临时内存（估算）
        batch_temp_memory = self.batch_size * (avg_keyword_size + avg_input_size) * self.max_workers

        total_estimated = base_memory + index_memory + batch_temp_memory

        self.logger.debug(f"内存估算: 基础={base_memory/1024/1024:.1f}MB, "
                         f"索引={index_memory/1024/1024:.1f}MB, "
                         f"批处理临时={batch_temp_memory/1024/1024:.1f}MB")

        return int(total_estimated)

    def _select_optimal_strategy(self, total_combinations: int, memory_estimate: int) -> str:
        """
        选择最优处理策略
        🚀 性能优化：调整策略选择逻辑，优先使用高效的parallel策略

        Args:
            total_combinations: 总组合数
            memory_estimate: 预估内存使用量

        Returns:
            str: 策略名称 ('serial', 'parallel', 'chunked')
        """
        # 获取系统可用内存（简单估算）
        import psutil
        available_memory = psutil.virtual_memory().available

        # 🚀 内存换效率优化：根据可用内存激进调整策略选择
        available_memory_gb = available_memory / (1024**3)

        if total_combinations < 5000:
            return 'serial'  # 小数据量用串行
        elif available_memory_gb >= 50:  # 50GB+内存：激进使用parallel
            if total_combinations > 50000000:  # 超过5000万组合才用分块
                return 'chunked'
            elif memory_estimate > available_memory * 0.7:  # 超过70%内存才分块
                return 'chunked'
            else:
                return 'parallel'  # 大幅扩展parallel使用范围
        elif available_memory_gb >= 20:  # 20GB+内存：扩展parallel范围
            if total_combinations > 20000000:  # 超过2000万组合用分块
                return 'chunked'
            elif memory_estimate > available_memory * 0.6:  # 超过60%内存用分块
                return 'chunked'
            else:
                return 'parallel'
        else:  # 标准内存：保守策略
            if total_combinations > 10000000:  # 超过1000万组合用分块
                return 'chunked'
            elif memory_estimate > available_memory * 0.5:  # 超过50%内存用分块
                return 'chunked'
            else:
                return 'parallel'

    def _calculate_optimal_parallel_config(self, total_combinations: int) -> tuple:
        """
        根据数据规模智能选择最优并行配置

        Args:
            total_combinations: 总组合数

        Returns:
            tuple: (推荐并行模式, 推荐批处理大小)
        """
        if not self.enable_optimization or self.max_workers <= 1:
            return 'serial', total_combinations

        # 根据数据规模选择最优策略
        if total_combinations < 1000:
            # 小数据量：串行处理最快
            return 'serial', total_combinations
        elif total_combinations < 10000:
            # 中等数据量：线程池并行
            optimal_batch_size = max(50, total_combinations // (self.max_workers * 3))
            return 'thread', min(200, optimal_batch_size)
        else:
            # 大数据量：进程池并行，使用合理的大批处理大小
            optimal_batch_size = max(100000, total_combinations // (self.max_workers * 2))
            return 'process', min(500000, optimal_batch_size)

    def _calculate_optimal_batch_size(self, total_combinations: int) -> int:
        """
        计算最优批处理大小

        Args:
            total_combinations: 总组合数

        Returns:
            int: 最优批处理大小
        """
        if not self.enable_optimization or self.max_workers <= 1:
            return total_combinations  # 串行处理时使用全部数据

        # 🚀 优化：动态批处理大小计算，减少进程间通信频率

        if self.parallel_mode == 'process':
            # 🚀 修复：合理的批处理大小，平衡通信开销和内存使用
            min_batches_per_worker = 2  # 恢复到合理值
            target_total_batches = self.max_workers * min_batches_per_worker

            # 🚀 修复：使用更大的批处理大小减少进程间通信
            optimal_size = max(100000, total_combinations // target_total_batches)  # 大幅增加批处理大小
            min_batch_size = 50000   # 合理的最小批处理大小
            max_batch_size = 500000  # 恢复到合理的最大批处理大小
        else:
            # 线程池：使用中等的批处理大小
            min_batches_per_worker = 4  # 从3增加到4
            target_total_batches = self.max_workers * min_batches_per_worker

            optimal_size = max(1000, total_combinations // target_total_batches)
            min_batch_size = 1000  # 从50提升到1000
            max_batch_size = 10000  # 从1000提升到10000

        optimal_size = max(min_batch_size, min(max_batch_size, optimal_size))

        self.logger.info(f"批处理优化: 模式={self.parallel_mode}, 总组合={total_combinations}, 目标批次数={target_total_batches}, 优化大小={optimal_size}")

        return optimal_size

    def _select_optimal_strategy_with_mode(self, total_combinations: int, memory_estimate: float) -> Tuple[str, str]:
        """
        🚀 优化：智能选择处理策略和并行模式

        Args:
            total_combinations: 总组合数
            memory_estimate: 内存估算

        Returns:
            Tuple[str, str]: (策略, 并行模式)
        """
        # 获取系统信息（用于未来扩展）
        # import psutil
        # available_memory = psutil.virtual_memory().available

        # 🚀 混合模式智能选择逻辑
        if self.parallel_mode == 'hybrid':
            if total_combinations < 1000000:  # 小于100万组合使用线程池
                return 'parallel', 'thread'
            elif total_combinations < 10000000:  # 小于1000万组合使用进程池
                return 'parallel', 'process'
            else:  # 大于1000万组合使用混合策略
                return 'parallel', 'hybrid'

        # 原有逻辑保持不变
        strategy = self._select_optimal_strategy(total_combinations, memory_estimate)
        return strategy, self.parallel_mode

    def _execute_hybrid_parallel_matching(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame,
                                        keyword_dicts: List[Dict], input_dicts: List[Dict],
                                        batch_size: int) -> List[Dict[str, Any]]:
        """
        🚀 新增：混合并行处理策略

        结合线程池和进程池的优势：
        - 数据预处理使用线程池（I/O密集型）
        - 正则表达式匹配使用进程池（CPU密集型）
        - 结果汇总使用线程池（I/O密集型）

        Args:
            keyword_df: 关键词数据表格
            input_df: 输入数据表格
            keyword_dicts: 预处理的关键词字典列表
            input_dicts: 预处理的输入数据字典列表
            batch_size: 批处理大小

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        self.logger.info("🚀 启动混合并行处理策略")

        # 第一阶段：使用线程池进行数据预处理（如果需要）
        start_time = time.time()

        # 第二阶段：使用进程池进行CPU密集型匹配
        self.logger.info("🚀 阶段2: 使用进程池进行正则表达式匹配")
        original_mode = self.parallel_mode
        self.parallel_mode = 'process'  # 临时切换到进程模式

        try:
            matching_results = self._execute_streaming_parallel_matching(
                keyword_df, input_df, keyword_dicts, input_dicts, batch_size
            )
        finally:
            self.parallel_mode = original_mode  # 恢复原始模式

        # 第三阶段：使用线程池进行结果后处理（如果需要）
        processing_time = time.time() - start_time
        self.logger.info(f"🚀 混合并行处理完成，耗时: {processing_time:.2f}秒")

        return matching_results

    def _process_combination_batch(self, combination_batch: List[Tuple]) -> List[Dict[str, Any]]:
        """
        处理组合批次 - 新的并行处理方法

        Args:
            combination_batch: 包含(keyword_idx, keyword_row, input_row)元组的列表
            batch_idx: 批次索引

        Returns:
            List[Dict[str, Any]]: 匹配结果列表
        """
        batch_results = []

        # 在子进程/线程中重新初始化组件（避免序列化问题）
        import logging
        logger = logging.getLogger(__name__)

        # 重新初始化子模块
        from core.filter_logic import FilterLogic
        from core.matching_engine import MatchingEngine
        from config import config_manager

        filter_logic = FilterLogic()
        matching_engine = MatchingEngine()

        for keyword_idx, keyword_row_dict, input_row_dict in combination_batch:
            try:
                # 数据已经是字典格式，直接使用
                keyword_dict = keyword_row_dict
                input_dict = input_row_dict

                # 应用筛选逻辑
                filter_result = filter_logic.apply_combined_filter(keyword_dict, input_dict)
                if not filter_result.passed:
                    continue

                # 执行关键词匹配
                match_result = matching_engine.match_keywords(
                    keyword_dict, input_dict, filter_result.filtered_columns
                )

                if match_result.success:
                    # 构建结果字典
                    result = {
                        'keyword_index': keyword_idx,
                        'keyword_rule_id': keyword_dict.get('id', keyword_idx),
                        'keyword_rule_name': keyword_dict.get('name', f'规则{keyword_idx}'),
                        'match_success': True,
                        'matched_texts': match_result.matched_texts,
                        'match_details': match_result.match_details,  # 添加完整的match_details
                        'total_texts_processed': match_result.match_details.get('total_texts_processed', 0),
                        'texts_passed_all_stages': match_result.match_details.get('texts_passed_all_stages', 0),
                        'like_passed_count': match_result.match_details.get('stage_stats', {}).get('like_passed', 0),
                        'must_passed_count': match_result.match_details.get('stage_stats', {}).get('must_passed', 0),
                        'unlike_passed_count': match_result.match_details.get('stage_stats', {}).get('unlike_passed', 0),
                        'like_failed_count': match_result.match_details.get('stage_stats', {}).get('like_failed', 0),
                        'must_failed_count': match_result.match_details.get('stage_stats', {}).get('must_failed', 0),
                        'unlike_failed_count': match_result.match_details.get('stage_stats', {}).get('unlike_failed', 0)
                    }

                    # 添加输入数据的标识列
                    identifier_columns = config_manager.get_list('keyword_matching.input_table_columns.identifier_columns')
                    for col in identifier_columns:
                        if col in input_dict:
                            result[col] = input_dict[col]

                    # 添加配置化的标识字段（用于过滤验证）
                    result[self.keyword_identifier_field] = keyword_idx
                    result[self.company_identifier_field] = input_dict.get('lc_company_id', '')

                    batch_results.append(result)

            except Exception as e:
                logger.error(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")

        return batch_results
    
    def _process_single_combination(self, keyword_idx: int, keyword_row: pd.Series, input_row: pd.Series) -> Optional[Dict[str, Any]]:
        """
        处理单个关键词-输入数据组合

        Args:
            keyword_idx: 关键词索引
            keyword_row: 关键词行数据
            input_row: 输入行数据

        Returns:
            Dict[str, Any]: 匹配结果，只有匹配成功时才返回，失败时返回None
        """
        try:
            # 转换为字典格式
            keyword_dict = keyword_row.to_dict()
            input_dict = input_row.to_dict()

            # 应用筛选逻辑
            filter_result = self.filter_logic.apply_combined_filter(keyword_dict, input_dict)
            if not filter_result.passed:
                self.progress.failed_matches += 1
                return None

            # 执行关键词匹配
            match_result = self.matching_engine.match_keywords(
                keyword_dict, input_dict, filter_result.filtered_columns
            )

            # 只有匹配成功的记录才构建结果并返回
            if match_result.success:
                # 提取match_details中的关键统计信息
                match_details = match_result.match_details or {}
                stage_stats = match_details.get('stage_stats', {})

                # 构建包含展开统计信息的匹配结果
                result = {
                    # 标识信息（使用配置化字段名称）
                    self.keyword_identifier_field: keyword_idx,  # 关键词规则标识
                    self.company_identifier_field: input_dict.get('lc_company_id', ''),  # 企业标识（修复字段名）

                    # 添加所有标识列
                    'lc_company_id': input_dict.get('lc_company_id', ''),
                    'company_name': input_dict.get('company_name', ''),

                    # 匹配结果信息
                    'match_success': True,
                    'matched_texts': match_result.matched_texts,  # 具体匹配到的文本内容和标记
                    'match_details': match_details,  # 保留完整的详细匹配统计信息

                    # 展开的统计信息字段
                    'total_texts_processed': match_details.get('total_texts_processed', 0),
                    'texts_passed_all_stages': match_details.get('texts_passed_all_stages', 0),
                    'like_passed_count': stage_stats.get('like_passed', 0),
                    'must_passed_count': stage_stats.get('must_passed', 0),
                    'unlike_passed_count': stage_stats.get('unlike_passed', 0),
                    'like_failed_count': stage_stats.get('like_failed', 0),
                    'must_failed_count': stage_stats.get('must_failed', 0),
                    'unlike_failed_count': stage_stats.get('unlike_failed', 0),

                    # 筛选信息
                    'filter_reason': filter_result.reason,
                    'filtered_columns': filter_result.filtered_columns,

                    # 处理时间戳
                    'processed_at': datetime.now().isoformat()
                }

                self.progress.successful_matches += 1
                return result
            else:
                # 匹配失败的记录不保留在最终结果中
                self.progress.failed_matches += 1
                return None

        except Exception as e:
            self.logger.warning(f"处理组合失败 (keyword_idx={keyword_idx}): {e}")
            self.progress.failed_matches += 1
            return None
    
    def _auto_detect_keyword_file(self) -> Optional[str]:
        """自动检测关键词文件"""
        # 查找最新的转换后关键词文件
        current_dir = Path('.')
        pattern = 'converted_keyword_rules*.xlsx'
        
        files = list(current_dir.glob(pattern))
        if files:
            # 按修改时间排序，返回最新的
            latest_file = max(files, key=lambda f: f.stat().st_mtime)
            return str(latest_file)
        
        return None

    def _get_or_compute_data_dicts(self, keyword_df: pd.DataFrame, input_df: pd.DataFrame) -> Tuple[List[Dict], List[Dict]]:
        """
        🚀 内存映射优化：高效预计算数据结构并存储到共享内存

        Args:
            keyword_df: 关键词DataFrame
            input_df: 输入数据DataFrame

        Returns:
            Tuple[List[Dict], List[Dict]]: (关键词字典列表, 输入数据字典列表)
        """
        self.logger.info("🚀 开始预计算数据结构（共享内存优化）...")
        start_time = time.time()

        # 🚀 内存映射优化：使用共享内存存储企业数据
        try:
            memory_manager = get_shared_memory_manager()

            # 转换数据
            keyword_dicts = keyword_df.to_dict('records')
            input_dicts = input_df.to_dict('records')

            # 🚀 预处理优化：缓存常用字段，减少字典查找开销
            for i, kw_dict in enumerate(keyword_dicts):
                kw_dict['_cached_id'] = kw_dict.get('id', i)
                kw_dict['_cached_name'] = kw_dict.get('name', '')

            for i, input_dict in enumerate(input_dicts):
                input_dict['_cached_id'] = input_dict.get('id', i)

            # 🚀 内存映射优化：将企业数据存储到共享内存
            if self.enable_shared_memory and len(input_dicts) > self.shared_memory_threshold:  # 只对大数据集使用共享内存
                try:
                    company_block_name = memory_manager.create_company_data_block(input_dicts)
                    self.logger.info(f"🚀 企业数据已存储到共享内存: {company_block_name}")
                    # 存储块名称以便后续访问
                    self._company_data_block = company_block_name
                except Exception as e:
                    self.logger.warning(f"共享内存存储失败，使用常规内存: {e}")
                    self._company_data_block = None
            else:
                self._company_data_block = None

            preprocess_time = time.time() - start_time
            self.logger.info(f"🚀 预计算完成: {len(keyword_dicts)} 个关键词, {len(input_dicts)} 个输入数据, 耗时: {preprocess_time:.3f}秒")

            return keyword_dicts, input_dicts

        except Exception as e:
            self.logger.warning(f"共享内存优化失败，回退到常规方法: {e}")
            # 回退到原有方法
            keyword_dicts = keyword_df.to_dict('records')
            input_dicts = input_df.to_dict('records')

            for i, kw_dict in enumerate(keyword_dicts):
                kw_dict['_cached_id'] = kw_dict.get('id', i)
                kw_dict['_cached_name'] = kw_dict.get('name', '')

            for i, input_dict in enumerate(input_dicts):
                input_dict['_cached_id'] = input_dict.get('id', i)

            preprocess_time = time.time() - start_time
            self.logger.info(f"🚀 预计算完成（回退模式）: {len(keyword_dicts)} 个关键词, {len(input_dicts)} 个输入数据, 耗时: {preprocess_time:.3f}秒")

            return keyword_dicts, input_dicts

    def _log_progress(self):
        """输出进度信息"""
        if self.progress.total_combinations > 0:
            progress_pct = (self.progress.processed_combinations / self.progress.total_combinations) * 100
            self.logger.info(f"匹配进度: {progress_pct:.1f}% "
                           f"({self.progress.processed_combinations}/{self.progress.total_combinations}), "
                           f"成功: {self.progress.successful_matches}, "
                           f"失败: {self.progress.failed_matches}")
    
    def run_complete_matching(self, input_file_path: str,
                            keyword_file_path: Optional[str] = None,
                            output_file_path: Optional[str] = None) -> Tuple[str, str]:
        """
        运行完整的匹配流程

        Args:
            input_file_path: 输入文件路径
            keyword_file_path: 关键词文件路径（可选）
            output_file_path: 输出文件路径（可选）

        Returns:
            Tuple[str, str]: (结果文件路径, 摘要报告路径)
        """
        try:
            # 加载数据
            self.logger.info("开始加载数据...")
            keyword_df = self.load_keyword_data(keyword_file_path)
            input_df = self.load_input_data(input_file_path)

            # 执行匹配
            self.logger.info("开始执行匹配...")
            matching_results = self.execute_matching(keyword_df, input_df)

            # 过滤和验证匹配结果
            self.logger.info("过滤和验证匹配结果...")
            successful_results = self._filter_successful_results(matching_results)

            # 记录结果统计
            total_results = len(matching_results) if matching_results else 0
            successful_count = len(successful_results)
            self.logger.info(f"匹配结果统计: 总处理 {total_results} 条组合, "
                           f"成功匹配 {successful_count} 条, "
                           f"成功率: {(successful_count/total_results*100):.2f}%" if total_results > 0 else "成功率: 0%")

            # 处理和保存结果
            self.logger.info("开始处理和保存结果...")
            result_file_path = self.result_processor.process_and_save_results(
                successful_results,
                output_file_path
            )

            # 生成摘要报告
            self.logger.info("生成摘要报告...")
            summary_report = self.result_processor.generate_summary_report(successful_results)
            report_file_path = self.result_processor.save_summary_report(summary_report)

            self.logger.info(f"完整匹配流程执行完成")
            self.logger.info(f"结果文件: {result_file_path}")
            self.logger.info(f"摘要报告: {report_file_path}")

            return result_file_path, report_file_path

        except Exception as e:
            self.logger.error(f"完整匹配流程执行失败: {e}")
            raise

    def _filter_successful_results(self, matching_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤出成功匹配的结果，确保数据完整性

        Args:
            matching_results: 原始匹配结果列表

        Returns:
            List[Dict[str, Any]]: 过滤后的成功匹配结果列表
        """
        if not matching_results:
            return []

        successful_results = []

        for result in matching_results:
            # 验证结果的完整性
            if (result and
                result.get('match_success') is True and
                result.get('matched_texts') and
                result.get('match_details')):

                # 确保包含必要的标识信息（使用配置化字段名称）
                if (self.keyword_identifier_field in result and
                    self.company_identifier_field in result):

                    successful_results.append(result)
                else:
                    keyword_id = result.get(self.keyword_identifier_field, 'unknown')
                    self.logger.warning(f"匹配结果缺少必要的标识信息: {keyword_id}")
            else:
                # 记录被过滤的原因
                if result:
                    reason = "未知原因"
                    if not result.get('match_success'):
                        reason = "匹配失败"
                    elif not result.get('matched_texts'):
                        reason = "无匹配文本"
                    elif not result.get('match_details'):
                        reason = "缺少匹配详情"

                    keyword_id = result.get(self.keyword_identifier_field, 'unknown')
                    self.logger.debug(f"过滤结果 (keyword_id={keyword_id}): {reason}")

        return successful_results
    
    def get_progress_info(self) -> MatchingProgress:
        """获取当前进度信息"""
        return self.progress


def test_keyword_matcher():
    """测试关键词匹配器"""
    print("=" * 60)
    print("关键词匹配器测试")
    print("=" * 60)

    matcher = KeywordMatcher()

    # 这里需要实际的测试文件
    print("注意：需要提供实际的测试文件进行完整测试")
    print(f"当前配置:")
    print(f"  关键词文件: {matcher.keyword_file_path}")
    print(f"  输入文件: {matcher.input_file_path}")
    print(f"  批处理大小: {matcher.batch_size}")
    print(f"  最大工作线程: {matcher.max_workers}")
    print(f"  性能优化: {matcher.enable_optimization}")


def test_single_combination_processing():
    """测试单个组合处理和结果保存的完整逻辑

    测试范围：
    1. 单个组合匹配测试 - 验证匹配逻辑和字段结构
    2. 结果保存逻辑测试 - 验证完整的数据处理流程
    3. 配置化字段名称验证 - 确认字段名称正确使用
    4. match_details详细信息保存验证
    """
    print("\n" + "=" * 80)
    print("单个组合处理和结果保存测试 - 验证完整数据处理流程")
    print("=" * 80)

    matcher = KeywordMatcher()

    # 创建测试数据 - 包含完整的筛选字段
    keyword_data = {
        'converted_like_keyword': '[0, "电动"]',
        'like_keyword': '电动',
        'converted_must_keyword': '[0, "汽车"]',
        'must_keyword': '汽车',
        'converted_unlike_keyword': '0',  # 设为默认值，避免unlike匹配失败
        'unlike_keyword': '',
        # 行业筛选字段 - 修正匹配逻辑
        'industry_type': 'C36',  # 直接使用具体的行业代码
        'industry_l1_code': '',  # 设为空
        # 数据源范围筛选
        'source_scope': 'main_product,service_intro'  # 指定参与匹配的列
    }

    input_data = {
        'company_id': 'TEST001',
        'company_name': '测试新能源汽车公司',
        'main_product': ['电动汽车', '充电桩'],
        'service_intro': ['新能源技术开发'],
        'industry': '制造业',
        'region': '广东省深圳市',
        # 行业代码字段
        'industry_l1_code': 'C36',  # 汽车制造业
        'industry_type': 'C'  # 制造业
    }

    # 转换为pandas Series
    keyword_row = pd.Series(keyword_data)
    input_row = pd.Series(input_data)
    input_row.name = 0  # 设置行索引

    # 测试处理单个组合
    print("测试数据:")
    print(f"关键词规则: like='{keyword_data['like_keyword']}', must='{keyword_data['must_keyword']}', unlike='{keyword_data['unlike_keyword']}'")
    print(f"输入数据: company='{input_data['company_name']}', products={input_data['main_product']}")

    # 先测试筛选逻辑
    keyword_dict = keyword_row.to_dict()
    input_dict = input_row.to_dict()
    filter_result = matcher.filter_logic.apply_combined_filter(keyword_dict, input_dict)
    print(f"\n筛选结果: passed={filter_result.passed}, reason='{filter_result.reason}'")
    print(f"筛选后的列: {filter_result.filtered_columns}")

    if filter_result.passed:
        # 测试匹配引擎
        match_result = matcher.matching_engine.match_keywords(
            keyword_dict, input_dict, filter_result.filtered_columns
        )
        print(f"匹配引擎结果: success={match_result.success}")
        if match_result.success:
            print(f"匹配文本: {match_result.matched_texts}")
            print(f"匹配详情: {match_result.match_details}")

    # 测试完整的组合处理
    result = matcher._process_single_combination(0, keyword_row, input_row)

    if result:
        print(f"\n✅ 匹配结果: 成功")
        print(f"匹配文本: {result['matched_texts']}")
        print(f"匹配详情keys: {list(result['match_details'].keys())}")
        print(f"包含完整信息:")

        # 使用配置化的字段名称
        keyword_field = matcher.keyword_identifier_field
        company_field = matcher.company_identifier_field

        print(f"  - {keyword_field}: {result[keyword_field]}")
        print(f"  - {company_field}: {result[company_field]}")
        print(f"  - 处理时间: {result['processed_at']}")

        # 验证match_details的完整性
        match_details = result['match_details']
        if 'total_texts_processed' in match_details:
            print(f"  - 处理文本总数: {match_details['total_texts_processed']}")
        if 'texts_passed_all_stages' in match_details:
            print(f"  - 通过所有阶段的文本数: {match_details['texts_passed_all_stages']}")
        if 'column_stats' in match_details:
            print(f"  - 列级统计: 简化显示（包含{len(match_details['column_stats'])}列的统计信息）")

        # 验证扩展后的字段结构（包含展开的统计信息）
        expected_core_fields = {
            keyword_field, company_field, 'match_success', 'matched_texts',
            'match_details', 'filter_reason', 'filtered_columns', 'processed_at'
        }
        expected_stats_fields = {
            'total_texts_processed', 'texts_passed_all_stages',
            'like_passed_count', 'must_passed_count', 'unlike_passed_count',
            'like_failed_count', 'must_failed_count', 'unlike_failed_count'
        }
        expected_all_fields = expected_core_fields | expected_stats_fields

        actual_fields = set(result.keys())
        print(f"  - 字段验证: 期望{len(expected_all_fields)}个字段，实际{len(actual_fields)}个字段")

        missing_core = expected_core_fields - actual_fields
        missing_stats = expected_stats_fields - actual_fields
        extra = actual_fields - expected_all_fields

        if not missing_core and not missing_stats:
            print(f"  - ✅ 字段结构完整（核心字段 + 统计字段）")
        else:
            if missing_core:
                print(f"  - ❌ 缺少核心字段: {missing_core}")
            if missing_stats:
                print(f"  - ❌ 缺少统计字段: {missing_stats}")

        if extra:
            print(f"  - ℹ️ 额外字段: {extra}")

        # 验证统计数据的准确性
        if all(field in result for field in expected_stats_fields):
            total_processed = result['total_texts_processed']
            like_total = result['like_passed_count'] + result['like_failed_count']
            print(f"  - 统计验证: 处理总数={total_processed}, like总数={like_total}")
            if total_processed == like_total:
                print(f"  - ✅ 统计数据一致性正确")
            else:
                print(f"  - ❌ 统计数据不一致")
    else:
        print(f"\n❌ 匹配结果: 失败或被过滤")

    # 测试失败案例
    print(f"\n测试失败案例:")
    input_data_fail = {
        'company_id': 'TEST002',
        'company_name': '传统机械公司',
        'main_product': ['传统设备', '机械产品'],
        'service_intro': ['传统制造'],
        'industry': '制造业',
        'region': '广东省深圳市'
    }

    input_row_fail = pd.Series(input_data_fail)
    input_row_fail.name = 1
    result_fail = matcher._process_single_combination(1, keyword_row, input_row_fail)

    if result_fail:
        print(f"❌ 意外的成功匹配: {result_fail}")
    else:
        print(f"✅ 正确的失败结果: 不符合匹配条件的记录被正确过滤")

    # ========== 第二部分：结果保存逻辑测试 ==========
    print(f"\n【第二部分】结果保存逻辑测试")
    print("-" * 50)

    # 创建包含多个匹配结果的测试数据集
    test_results = []

    # 添加成功匹配的结果
    if result:
        test_results.append(result)

    # 创建更多测试数据
    additional_test_cases = [
        {
            'company_id': 'TEST003',
            'company_name': '新能源科技公司',
            'main_product': ['电动摩托车', '电池技术'],
            'service_intro': ['电动车辆研发'],
            'industry': '制造业',
            'region': '上海市',
            'industry_l1_code': 'C36',
            'industry_type': 'C'
        },
        {
            'company_id': 'TEST004',
            'company_name': '智能汽车公司',
            'main_product': ['智能电动汽车', '自动驾驶'],
            'service_intro': ['汽车智能化'],
            'industry': '制造业',
            'region': '北京市',
            'industry_l1_code': 'C36',
            'industry_type': 'C'
        }
    ]

    # 处理额外的测试案例
    for i, additional_data in enumerate(additional_test_cases, start=2):
        additional_row = pd.Series(additional_data)
        additional_row.name = i
        additional_result = matcher._process_single_combination(i, keyword_row, additional_row)
        if additional_result:
            test_results.append(additional_result)

    print(f"创建测试数据集: 共{len(test_results)}条成功匹配的记录")

    # 测试结果过滤逻辑
    print(f"\n测试结果过滤逻辑:")
    filtered_results = matcher._filter_successful_results(test_results)
    print(f"过滤前: {len(test_results)}条记录")
    print(f"过滤后: {len(filtered_results)}条记录")
    print(f"过滤效果: {'✅ 正常' if len(filtered_results) == len(test_results) else '❌ 异常'}")

    # 测试结果保存
    print(f"\n测试结果保存:")
    try:
        # 调用结果处理器保存结果
        output_file_path = matcher.result_processor.process_and_save_results(
            filtered_results,
            output_path=None  # 使用默认路径
        )

        print(f"✅ 结果保存成功")
        print(f"保存文件路径: {output_file_path}")

        # 验证保存的文件
        if Path(output_file_path).exists():
            file_size = Path(output_file_path).stat().st_size
            print(f"文件大小: {file_size} 字节")

            # 读取并验证保存的结果
            try:
                saved_df = pd.read_csv(output_file_path)
                print(f"保存的记录数: {len(saved_df)}")
                print(f"保存的字段: {list(saved_df.columns)}")

                # 验证配置化字段名称
                keyword_field = matcher.keyword_identifier_field
                company_field = matcher.company_identifier_field

                if keyword_field in saved_df.columns:
                    print(f"✅ 配置化关键词字段 '{keyword_field}' 存在")
                else:
                    print(f"❌ 配置化关键词字段 '{keyword_field}' 缺失")

                if company_field in saved_df.columns:
                    print(f"✅ 配置化企业字段 '{company_field}' 存在")
                else:
                    print(f"❌ 配置化企业字段 '{company_field}' 缺失")

                # 验证结果处理器的输出格式
                print(f"\n结果处理器输出格式验证:")

                # 检查匹配文本列（结果处理器将matched_texts展开为多个列）
                matched_text_columns = [col for col in saved_df.columns if col.endswith('_matched_texts')]
                if matched_text_columns:
                    print(f"✅ 匹配文本列已展开: 共{len(matched_text_columns)}列")
                    print(f"   示例列: {matched_text_columns[:3]}...")
                else:
                    print(f"❌ 匹配文本列缺失")

                # 验证必要的标识字段
                required_fields = {keyword_field, company_field}
                missing_required = required_fields - set(saved_df.columns)
                if not missing_required:
                    print(f"✅ 必要标识字段完整: {required_fields}")
                else:
                    print(f"❌ 缺少必要字段: {missing_required}")

                # 验证统计信息字段
                expected_stats_fields = {
                    'total_texts_processed', 'texts_passed_all_stages',
                    'like_passed_count', 'must_passed_count', 'unlike_passed_count',
                    'like_failed_count', 'must_failed_count', 'unlike_failed_count'
                }
                stats_fields_in_csv = expected_stats_fields & set(saved_df.columns)
                missing_stats = expected_stats_fields - set(saved_df.columns)

                if not missing_stats:
                    print(f"✅ 统计信息字段完整: 共{len(stats_fields_in_csv)}个字段")
                    print(f"   统计字段: {sorted(stats_fields_in_csv)}")
                else:
                    print(f"❌ 缺少统计字段: {missing_stats}")
                    print(f"✅ 已有统计字段: {sorted(stats_fields_in_csv)}")

                # 验证数据完整性
                print(f"\n数据完整性验证:")
                non_empty_records = 0
                for _, row in saved_df.iterrows():
                    # 检查是否有非空的匹配文本
                    has_matches = any(row.get(col, '') for col in matched_text_columns)
                    if has_matches:
                        non_empty_records += 1

                print(f"有效匹配记录数: {non_empty_records}/{len(saved_df)}")
                if non_empty_records == len(saved_df):
                    print(f"✅ 所有保存的记录都包含匹配文本")
                else:
                    print(f"❌ 部分记录缺少匹配文本")

                # 显示示例记录
                print(f"\n示例记录（前2条）:")
                for i, (_, row) in enumerate(saved_df.head(2).iterrows()):
                    print(f"  记录{i+1}: {keyword_field}={row.get(keyword_field)}, {company_field}={row.get(company_field)}")

                    # 显示有内容的匹配文本列
                    non_empty_matches = []
                    for col in matched_text_columns:
                        value = row.get(col, '')
                        # 排除空值、NaN、空列表和空字符串
                        if value and str(value) not in ['', '[]', 'nan', 'NaN']:
                            non_empty_matches.append(f"{col}={value}")

                    if non_empty_matches:
                        print(f"           匹配内容: {non_empty_matches[0]}")  # 只显示第一个非空匹配
                        if len(non_empty_matches) > 1:
                            print(f"           (还有{len(non_empty_matches)-1}个其他匹配列)")
                    else:
                        print(f"           匹配内容: 无有效匹配")

                    # 显示统计信息
                    if stats_fields_in_csv:
                        stats_info = []
                        for field in ['total_texts_processed', 'texts_passed_all_stages', 'like_passed_count', 'must_passed_count']:
                            if field in row:
                                stats_info.append(f"{field}={row.get(field, 0)}")
                        if stats_info:
                            print(f"           统计信息: {', '.join(stats_info)}")

                # 验证原始结果数据的完整性（在内存中）
                print(f"\n原始结果数据验证:")
                if filtered_results:
                    sample_result = filtered_results[0]
                    original_fields = set(sample_result.keys())

                    # 核心字段
                    expected_core_fields = {
                        keyword_field, company_field, 'match_success', 'matched_texts',
                        'match_details', 'filter_reason', 'filtered_columns', 'processed_at'
                    }

                    # 展开的统计字段
                    expected_stats_fields = {
                        'total_texts_processed', 'texts_passed_all_stages',
                        'like_passed_count', 'must_passed_count', 'unlike_passed_count',
                        'like_failed_count', 'must_failed_count', 'unlike_failed_count'
                    }

                    expected_all_fields = expected_core_fields | expected_stats_fields

                    missing_core = expected_core_fields - original_fields
                    missing_stats = expected_stats_fields - original_fields

                    if not missing_core and not missing_stats:
                        print(f"✅ 原始结果数据结构完整（{len(expected_core_fields)}个核心字段 + {len(expected_stats_fields)}个统计字段）")
                        print(f"   总字段数: {len(original_fields)}")

                        # 验证match_details的存在
                        if 'match_details' in sample_result and sample_result['match_details']:
                            match_details = sample_result['match_details']
                            print(f"✅ match_details信息完整: {list(match_details.keys())}")
                        else:
                            print(f"❌ match_details信息缺失")

                        # 验证统计字段的数据一致性
                        total_processed = sample_result.get('total_texts_processed', 0)
                        like_passed = sample_result.get('like_passed_count', 0)
                        like_failed = sample_result.get('like_failed_count', 0)
                        like_total = like_passed + like_failed

                        print(f"✅ 统计数据示例: 总处理={total_processed}, like通过={like_passed}, like失败={like_failed}")
                        if total_processed == like_total:
                            print(f"✅ 统计数据一致性验证通过")
                        else:
                            print(f"❌ 统计数据不一致: {total_processed} != {like_total}")
                    else:
                        if missing_core:
                            print(f"❌ 缺少核心字段: {missing_core}")
                        if missing_stats:
                            print(f"❌ 缺少统计字段: {missing_stats}")
                else:
                    print(f"❌ 无原始结果数据可验证")

            except Exception as e:
                print(f"❌ 读取保存文件失败: {e}")
        else:
            print(f"❌ 保存文件不存在: {output_file_path}")

    except Exception as e:
        print(f"❌ 结果保存失败: {e}")

    print(f"\n{'='*80}")
    print(f"完整数据处理流程测试完成")
    print(f"{'='*80}")


if __name__ == "__main__":
    test_keyword_matcher()
    test_single_combination_processing()
