#!/usr/bin/env python3
"""
挂链工具集 - 主入口文件
Link Tool Suite - Main Entry Point

这是整个项目的统一入口点，提供对各个功能模块的访问
"""

import sys
import os
import time
import psutil
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入子功能模块
import cli.keyword_rules_validator_cli as keyword_rules_validator_cli
import cli.keyword_converter_cli as keyword_converter_cli
import cli.keyword_matcher_cli as keyword_matcher_cli
from core.keyword_matcher import KeywordMatcher


def show_main_menu():
    """显示主菜单"""
    print("=" * 60)
    print("挂链工具集 - 主菜单")
    print("=" * 60)
    print("\n请选择要使用的功能：\n")
    print("1. 关键词规则验证")
    print("2. 关键词模式转换")
    print("3. 智能关键词匹配")
    print("4. 退出程序")

    while True:
        try:
            choice = input("\n请输入选项编号 [1-4]: ")
            if choice in ['1', '2', '3', '4']:
                return choice
            else:
                print("❌ 无效的选项，请重新输入")
        except KeyboardInterrupt:
            print("\n程序已中断")
            return '4'


class PerformanceAnalyzer:
    """性能分析器 - 用于自动化性能测试"""

    def __init__(self):
        self.start_time = None
        self.step_times = {}
        self.memory_usage = {}
        self.process = psutil.Process()

    def start_timing(self, step_name: str):
        """开始计时"""
        current_time = time.time()
        if self.start_time is None:
            self.start_time = current_time

        self.step_times[step_name] = {'start': current_time}

        # 记录内存使用
        memory_info = self.process.memory_info()
        self.memory_usage[step_name] = {
            'start_rss': memory_info.rss / 1024 / 1024,  # MB
            'start_vms': memory_info.vms / 1024 / 1024   # MB
        }

    def end_timing(self, step_name: str):
        """结束计时"""
        current_time = time.time()
        if step_name in self.step_times:
            self.step_times[step_name]['end'] = current_time
            self.step_times[step_name]['duration'] = current_time - self.step_times[step_name]['start']

            # 记录结束时的内存使用
            memory_info = self.process.memory_info()
            self.memory_usage[step_name].update({
                'end_rss': memory_info.rss / 1024 / 1024,
                'end_vms': memory_info.vms / 1024 / 1024
            })

            # 计算内存增长
            self.memory_usage[step_name]['rss_growth'] = (
                self.memory_usage[step_name]['end_rss'] -
                self.memory_usage[step_name]['start_rss']
            )

    def get_total_time(self) -> float:
        """获取总耗时"""
        if self.start_time is None:
            return 0
        return time.time() - self.start_time

    def generate_report(self, total_combinations: int, results_count: int) -> Dict[str, Any]:
        """生成性能分析报告"""
        total_time = self.get_total_time()

        report = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'total_time': total_time,
            'total_combinations': total_combinations,
            'results_count': results_count,
            'processing_speed': total_combinations / total_time if total_time > 0 else 0,
            'step_analysis': {},
            'memory_analysis': {},
            'bottlenecks': [],
            'recommendations': []
        }

        # 分析各步骤耗时
        for step_name, timing in self.step_times.items():
            if 'duration' in timing:
                percentage = (timing['duration'] / total_time * 100) if total_time > 0 else 0
                report['step_analysis'][step_name] = {
                    'duration': timing['duration'],
                    'percentage': percentage,
                    'speed': total_combinations / timing['duration'] if timing['duration'] > 0 else 0
                }

        # 分析内存使用
        for step_name, memory in self.memory_usage.items():
            if 'rss_growth' in memory:
                report['memory_analysis'][step_name] = {
                    'start_memory_mb': memory['start_rss'],
                    'end_memory_mb': memory['end_rss'],
                    'memory_growth_mb': memory['rss_growth']
                }

        # 识别性能瓶颈
        if report['step_analysis']:
            # 找出最耗时的步骤
            slowest_step = max(report['step_analysis'].items(),
                             key=lambda x: x[1]['duration'])
            if slowest_step[1]['percentage'] > 40:
                report['bottlenecks'].append({
                    'type': 'time_bottleneck',
                    'step': slowest_step[0],
                    'percentage': slowest_step[1]['percentage'],
                    'description': f"{slowest_step[0]} 占用了 {slowest_step[1]['percentage']:.1f}% 的处理时间"
                })

        # 生成优化建议
        self._generate_recommendations(report)

        return report

    def _generate_recommendations(self, report: Dict[str, Any]):
        """生成优化建议"""
        recommendations = []

        # 基于处理速度的建议
        speed = report['processing_speed']
        if speed < 5000:
            recommendations.append("处理速度较慢，建议增加并行工作进程数量")
        elif speed < 10000:
            recommendations.append("处理速度一般，可考虑优化数据预处理步骤")
        else:
            recommendations.append("处理速度良好，系统性能表现正常")

        # 基于瓶颈的建议
        for bottleneck in report['bottlenecks']:
            if bottleneck['type'] == 'time_bottleneck':
                step = bottleneck['step']
                if 'data_loading' in step.lower():
                    recommendations.append("数据加载是瓶颈，建议优化文件读取方式或使用更快的存储")
                elif 'matching' in step.lower():
                    recommendations.append("匹配过程是瓶颈，建议优化匹配算法或增加并行度")
                elif 'preprocessing' in step.lower():
                    recommendations.append("数据预处理是瓶颈，建议优化DataFrame操作或使用更高效的数据结构")

        # 基于内存使用的建议
        max_memory = 0
        for memory_info in report['memory_analysis'].values():
            max_memory = max(max_memory, memory_info.get('end_memory_mb', 0))

        if max_memory > 8000:  # 8GB
            recommendations.append("内存使用较高，建议使用分块处理策略")
        elif max_memory > 4000:  # 4GB
            recommendations.append("内存使用适中，当前策略合适")
        else:
            recommendations.append("内存使用较低，可以考虑增加批处理大小以提高效率")

        report['recommendations'] = recommendations


def run_automated_performance_test():
    """运行自动化性能测试"""
    print("🚀 启动自动化性能测试模式")
    print("=" * 60)
    print("测试配置：")
    print("• 自动选择随机Parquet文件")
    print("• 限制数据规模：前500行输入数据（快速测试）")
    print("• 保持207个关键词规则")
    print("• 总测试规模：207 × 500 = 103,500 组合")
    print("• 详细性能分析和优化建议")
    print("=" * 60)

    # 初始化性能分析器
    analyzer = PerformanceAnalyzer()

    try:
        # 步骤1：初始化匹配器
        analyzer.start_timing("system_initialization")
        matcher = KeywordMatcher()

        # 启用性能分析模式
        matcher.enable_profiling = True

        if not matcher.parquet_enabled:
            print("❌ Parquet数据源未启用，无法进行自动化测试")
            return

        analyzer.end_timing("system_initialization")

        # 步骤2：自动选择关键词文件
        analyzer.start_timing("keyword_file_detection")

        # 自动检测关键词文件
        keyword_files = []
        for file in os.listdir('.'):
            if file.startswith('converted_keyword_rules') and file.endswith('.xlsx'):
                keyword_files.append(file)

        if not keyword_files:
            print("❌ 未找到关键词规则文件（converted_keyword_rules*.xlsx）")
            return

        # 选择最新的关键词文件
        keyword_file = max(keyword_files, key=lambda f: os.path.getmtime(f))
        print(f"✓ 自动选择关键词文件: {keyword_file}")

        analyzer.end_timing("keyword_file_detection")

        # 步骤3：数据加载
        analyzer.start_timing("data_loading")

        # 随机选择测试文件
        available_files = matcher.get_available_parquet_files()
        if not available_files:
            print("❌ 未找到可用的Parquet文件")
            return

        import random
        test_file = random.choice(available_files)
        print(f"✓ 随机选择测试文件: {test_file}")

        # 加载关键词数据
        keyword_df = pd.read_excel(keyword_file)
        print(f"✓ 加载关键词数据: {len(keyword_df)} 行")

        # 加载并限制输入数据
        input_df = matcher.load_parquet_data_from_folders(test_file)
        original_rows = len(input_df)

        # 限制为前500行（快速测试）
        test_rows = min(500, original_rows)
        input_df = input_df.head(test_rows)
        print(f"✓ 加载输入数据: {original_rows} 行，测试使用: {test_rows} 行")

        total_combinations = len(keyword_df) * len(input_df)
        print(f"✓ 总测试组合: {total_combinations:,}")

        analyzer.end_timing("data_loading")

        # 步骤4：数据预处理
        analyzer.start_timing("data_preprocessing")

        # 预处理数据（模拟匹配器内部的预处理步骤）
        keyword_dicts = [row.to_dict() for _, row in keyword_df.iterrows()]
        input_dicts = [row.to_dict() for _, row in input_df.iterrows()]

        print(f"✓ 数据预处理完成")
        analyzer.end_timing("data_preprocessing")

        # 步骤5：执行匹配
        analyzer.start_timing("matching_execution")
        print(f"\n开始执行匹配... (时间: {datetime.now().strftime('%H:%M:%S')})")

        # 执行匹配
        results = matcher.execute_matching(keyword_df, input_df)

        analyzer.end_timing("matching_execution")

        # 步骤6：结果处理
        analyzer.start_timing("result_processing")

        successful_results = [r for r in results if r.get('match_success', False)]

        print(f"✓ 匹配执行完成")
        print(f"  - 总结果数: {len(results)}")
        print(f"  - 成功匹配: {len(successful_results)}")
        print(f"  - 匹配率: {len(successful_results)/len(results)*100:.2f}%" if results else "0%")

        analyzer.end_timing("result_processing")

        # 生成性能分析报告
        print(f"\n📊 生成性能分析报告...")
        report = analyzer.generate_report(total_combinations, len(results))

        # 显示性能分析结果
        display_performance_report(report)

        # 保存详细报告
        save_performance_report(report, test_file, keyword_file)

    except Exception as e:
        print(f"❌ 自动化测试失败: {e}")
        import traceback
        traceback.print_exc()


def display_performance_report(report: Dict[str, Any]):
    """显示性能分析报告"""
    print("\n" + "=" * 70)
    print("🎯 性能分析报告")
    print("=" * 70)

    # 总体性能
    print(f"\n📈 总体性能:")
    print(f"  总处理时间: {report['total_time']:.2f} 秒")
    print(f"  总组合数量: {report['total_combinations']:,}")
    print(f"  处理速度: {report['processing_speed']:.0f} 组合/秒")
    print(f"  结果数量: {report['results_count']:,}")

    # 各步骤耗时分析
    print(f"\n⏱️ 各步骤耗时分析:")
    for step_name, analysis in report['step_analysis'].items():
        print(f"  {step_name}:")
        print(f"    耗时: {analysis['duration']:.2f} 秒 ({analysis['percentage']:.1f}%)")
        print(f"    速度: {analysis['speed']:.0f} 组合/秒")

    # 内存使用分析
    print(f"\n💾 内存使用分析:")
    for step_name, memory in report['memory_analysis'].items():
        print(f"  {step_name}:")
        print(f"    起始内存: {memory['start_memory_mb']:.1f} MB")
        print(f"    结束内存: {memory['end_memory_mb']:.1f} MB")
        print(f"    内存增长: {memory['memory_growth_mb']:.1f} MB")

    # 性能瓶颈
    if report['bottlenecks']:
        print(f"\n🔍 性能瓶颈识别:")
        for bottleneck in report['bottlenecks']:
            print(f"  ⚠️ {bottleneck['description']}")
    else:
        print(f"\n✅ 未发现明显性能瓶颈")

    # 优化建议
    print(f"\n💡 优化建议:")
    for i, recommendation in enumerate(report['recommendations'], 1):
        print(f"  {i}. {recommendation}")


def save_performance_report(report: Dict[str, Any], test_file: str, keyword_file: str):
    """保存性能分析报告"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"performance_report_{timestamp}.json"

    # 添加测试配置信息
    report['test_config'] = {
        'test_file': test_file,
        'keyword_file': keyword_file,
        'test_mode': 'automated_performance_test',
        'data_limit': 500
    }

    import json
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)

    print(f"\n📄 详细报告已保存: {report_file}")


def main():
    """主函数"""
    # 检查是否为自动化测试模式
    if len(sys.argv) > 1 and sys.argv[1] == '--auto-test':
        run_automated_performance_test()
        return

    # 正常交互模式
    while True:
        choice = show_main_menu()

        if choice == '1':
            # 运行关键词规则验证器
            print("\n正在启动关键词规则验证器...\n")
            keyword_rules_validator_cli.main()
        elif choice == '2':
            # 运行关键词模式转换器
            print("\n正在启动关键词模式转换器...\n")
            keyword_converter_cli.main()
        elif choice == '3':
            # 运行智能关键词匹配器
            print("\n正在启动智能关键词匹配器...\n")
            keyword_matcher_cli.main()
        elif choice == '4':
            print("\n感谢使用挂链工具集，再见！")
            sys.exit(0)


if __name__ == "__main__":
    try:
        # 显示使用帮助
        if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
            print("""
挂链工具集 - 使用帮助

用法:
    python main.py                    # 运行交互式主菜单
    python main.py --auto-test        # 运行自动化性能测试
    python main.py --help             # 显示帮助信息

自动化性能测试说明:
    --auto-test 模式将自动执行以下操作：
    1. 自动选择智能关键词匹配功能
    2. 自动选择随机Parquet文件进行测试
    3. 自动使用检测到的关键词规则文件
    4. 限制测试数据为前500行（快速测试）
    5. 生成详细的性能分析报告和优化建议

    测试规模: 207个关键词 × 500行数据 = 103,500组合

    输出文件:
    - performance_report_YYYYMMDD_HHMMSS.json  # 详细性能报告

示例:
    python main.py --auto-test        # 快速性能测试
""")
            sys.exit(0)

        main()
    except KeyboardInterrupt:
        print("\n程序已中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 程序执行出错：{e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)