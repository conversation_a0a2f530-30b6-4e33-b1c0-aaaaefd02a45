#!/usr/bin/env python3
"""
正则表达式模式缓存优化验证测试
Pattern Cache Optimization Verification Test

验证删除缓存大小限制后的效果
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.matching_engine import MatchingEngine


def test_cache_size_unlimited():
    """测试缓存大小无限制"""
    print("=" * 60)
    print("缓存大小无限制验证测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建大量不同的模式进行测试
    test_patterns = []
    for i in range(200):  # 创建200个不同的模式
        test_patterns.append(f'[0, "关键词{i}"]')
    
    print(f"准备测试 {len(test_patterns)} 个不同的正则表达式模式...")
    
    # 获取初始缓存状态
    initial_cache_size = len(engine._pattern_cache)
    initial_hit_count = engine._cache_hit_count
    initial_miss_count = engine._cache_miss_count
    
    print(f"初始缓存状态:")
    print(f"- 缓存大小: {initial_cache_size}")
    print(f"- 缓存命中: {initial_hit_count}")
    print(f"- 缓存未命中: {initial_miss_count}")
    
    # 第一轮：编译所有模式（应该全部缓存未命中）
    start_time = time.time()
    for pattern in test_patterns:
        engine._get_cached_pattern(f"test_{pattern}", pattern)
    first_round_time = time.time() - start_time
    
    # 检查第一轮后的缓存状态
    after_first_cache_size = len(engine._pattern_cache)
    after_first_hit_count = engine._cache_hit_count
    after_first_miss_count = engine._cache_miss_count
    
    print(f"\n第一轮编译后的缓存状态:")
    print(f"- 缓存大小: {after_first_cache_size}")
    print(f"- 缓存命中: {after_first_hit_count}")
    print(f"- 缓存未命中: {after_first_miss_count}")
    print(f"- 编译时间: {first_round_time:.3f} 秒")
    
    # 第二轮：重复编译相同模式（应该全部缓存命中）
    start_time = time.time()
    for pattern in test_patterns:
        engine._get_cached_pattern(f"test_{pattern}", pattern)
    second_round_time = time.time() - start_time
    
    # 检查第二轮后的缓存状态
    after_second_cache_size = len(engine._pattern_cache)
    after_second_hit_count = engine._cache_hit_count
    after_second_miss_count = engine._cache_miss_count
    
    print(f"\n第二轮编译后的缓存状态:")
    print(f"- 缓存大小: {after_second_cache_size}")
    print(f"- 缓存命中: {after_second_hit_count}")
    print(f"- 缓存未命中: {after_second_miss_count}")
    print(f"- 编译时间: {second_round_time:.3f} 秒")
    
    # 计算性能提升
    if first_round_time > 0:
        speedup = first_round_time / second_round_time if second_round_time > 0 else float('inf')
        print(f"- 性能提升: {speedup:.1f}x")
    
    # 验证结果
    expected_cache_size = len(test_patterns)
    expected_hit_count = len(test_patterns)
    expected_miss_count = len(test_patterns)
    
    results = {
        'cache_size_correct': after_second_cache_size == expected_cache_size,
        'hit_count_correct': (after_second_hit_count - initial_hit_count) == expected_hit_count,
        'miss_count_correct': (after_second_miss_count - initial_miss_count) == expected_miss_count,
        'performance_improved': second_round_time < first_round_time
    }
    
    print(f"\n验证结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"- {test_name}: {status}")
    
    return all(results.values())


def test_cache_hit_rate():
    """测试缓存命中率"""
    print("\n" + "=" * 60)
    print("缓存命中率测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建一些重复的模式（使用相同的缓存键来测试重复）
    patterns_with_keys = [
        ('key_tech', '[0, "技术"]'),
        ('key_soft', '[0, "软件"]'),
        ('key_dev', '[0, "开发"]'),
        ('key_tech', '[0, "技术"]'),  # 重复键
        ('key_soft', '[0, "软件"]'),  # 重复键
        ('key_innovation', '[0, "创新"]'),
        ('key_tech', '[0, "技术"]'),  # 重复键
    ]

    print(f"测试模式: {len(patterns_with_keys)} 个（包含重复）")

    initial_hit_count = engine._cache_hit_count
    initial_miss_count = engine._cache_miss_count

    # 编译所有模式
    for cache_key, pattern in patterns_with_keys:
        engine._get_cached_pattern(cache_key, pattern)
    
    final_hit_count = engine._cache_hit_count
    final_miss_count = engine._cache_miss_count
    
    hit_count = final_hit_count - initial_hit_count
    miss_count = final_miss_count - initial_miss_count
    total_requests = hit_count + miss_count
    
    hit_rate = (hit_count / total_requests * 100) if total_requests > 0 else 0
    
    print(f"缓存统计:")
    print(f"- 总请求数: {total_requests}")
    print(f"- 缓存命中: {hit_count}")
    print(f"- 缓存未命中: {miss_count}")
    print(f"- 命中率: {hit_rate:.1f}%")
    print(f"- 最终缓存大小: {len(engine._pattern_cache)}")
    
    # 验证命中率合理（应该有重复模式被缓存命中）
    expected_hit_rate = 40  # 期望至少40%的命中率
    return hit_rate >= expected_hit_rate


def test_memory_efficiency():
    """测试内存效率"""
    print("\n" + "=" * 60)
    print("内存效率测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建大量模式测试内存使用
    large_pattern_count = 500
    patterns = [f'[0, "关键词{i}"]' for i in range(large_pattern_count)]
    
    print(f"测试 {large_pattern_count} 个不同模式的内存使用...")
    
    import psutil
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    # 编译所有模式
    start_time = time.time()
    for pattern in patterns:
        engine._get_cached_pattern(f"large_test_{pattern}", pattern)
    compile_time = time.time() - start_time
    
    final_memory = process.memory_info().rss / 1024 / 1024  # MB
    memory_growth = final_memory - initial_memory
    
    print(f"内存使用统计:")
    print(f"- 初始内存: {initial_memory:.2f} MB")
    print(f"- 最终内存: {final_memory:.2f} MB")
    print(f"- 内存增长: {memory_growth:.2f} MB")
    print(f"- 平均每个模式: {memory_growth/large_pattern_count*1024:.2f} KB")
    print(f"- 编译时间: {compile_time:.3f} 秒")
    print(f"- 缓存大小: {len(engine._pattern_cache)}")
    
    # 验证内存使用合理（每个模式应该占用很少内存）
    avg_memory_per_pattern = memory_growth / large_pattern_count * 1024  # KB
    memory_efficient = avg_memory_per_pattern < 10  # 每个模式少于10KB
    
    print(f"内存效率: {'✅ 高效' if memory_efficient else '❌ 需要优化'}")
    
    return memory_efficient


def main():
    """主函数"""
    print("🚀 正则表达式模式缓存优化验证测试")
    print("=" * 60)
    print("验证删除缓存大小限制后的效果")
    print()
    
    # 执行所有测试
    tests = [
        ("缓存大小无限制", test_cache_size_unlimited),
        ("缓存命中率", test_cache_hit_rate),
        ("内存效率", test_memory_efficiency)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    # 最终评估
    print("\n" + "=" * 60)
    print("最终评估结果")
    print("=" * 60)
    
    print(f"测试通过率: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 模式缓存优化完全成功！")
        print("✅ 删除了不必要的缓存大小限制")
        print("✅ 简化了缓存管理逻辑")
        print("✅ 保持了高缓存命中率")
        print("✅ 实现了高内存效率")
        print("✅ 最大化了性能收益")
    else:
        print("⚠️  模式缓存优化可能需要进一步调整")


if __name__ == "__main__":
    main()
