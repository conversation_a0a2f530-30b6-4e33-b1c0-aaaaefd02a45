#!/usr/bin/env python3
"""
性能优化验证测试
Performance Optimization Verification Test

验证删除 _clean_and_validate_text 和 _fast_keyword_prefilter 方法后的性能提升
"""

import sys
import numpy as np
from pathlib import Path
import time
import psutil

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.matching_engine import MatchingEngine


def test_method_removal():
    """测试方法是否已被完全删除"""
    print("=" * 60)
    print("方法删除验证测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 检查方法是否已删除
    removed_methods = [
        '_clean_and_validate_text',
        '_fast_keyword_prefilter',
        '_extract_simple_keywords_for_prefilter'
    ]
    
    found_methods = []
    for method_name in removed_methods:
        if hasattr(engine, method_name):
            found_methods.append(method_name)
    
    if found_methods:
        print(f"❌ 发现未删除的方法: {found_methods}")
        return False
    else:
        print("✅ 所有目标方法已成功删除")
        return True


def test_cache_attributes():
    """测试缓存属性是否已清理"""
    print("\n" + "=" * 60)
    print("缓存属性清理验证")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 检查缓存相关属性
    cache_attributes = []
    if hasattr(engine, '_text_cleaning_cache'):
        cache_attributes.append('_text_cleaning_cache')
    if hasattr(engine, '_text_cache_hit_count'):
        cache_attributes.append('_text_cache_hit_count')
    if hasattr(engine, '_text_cache_miss_count'):
        cache_attributes.append('_text_cache_miss_count')
    
    if cache_attributes:
        print(f"⚠️  发现残留的缓存属性: {cache_attributes}")
        for attr in cache_attributes:
            value = getattr(engine, attr)
            print(f"   {attr}: {value} (类型: {type(value)})")
        return False
    else:
        print("✅ 所有文本清理缓存属性已清理")
        return True


def test_functionality():
    """测试核心功能是否正常"""
    print("\n" + "=" * 60)
    print("核心功能验证测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建测试数据
    test_data = {
        'lc_company_id': 'test_001',
        'company_name': '测试科技有限公司',
        'company_profile': np.array(['专注于人工智能技术研发', '提供智能化解决方案']),
        'business_scope': np.array(['软件开发', '技术咨询', '系统集成']),
        'main_product': np.array(['AI平台', '智能分析系统'])
    }
    
    # 测试关键词匹配
    test_cases = [
        {
            'like_keyword': '[0, "技术"]',
            'converted_like_keyword': '[0, "技术"]',
            'must_keyword': '0',
            'converted_must_keyword': '0',
            'unlike_keyword': '0',
            'converted_unlike_keyword': '0',
            'expected': True
        },
        {
            'like_keyword': '[0, "医疗"]',
            'converted_like_keyword': '[0, "医疗"]',
            'must_keyword': '0',
            'converted_must_keyword': '0',
            'unlike_keyword': '0',
            'converted_unlike_keyword': '0',
            'expected': False
        },
        {
            'like_keyword': '[0, "ai"]',
            'converted_like_keyword': '[0, "AI"]',
            'must_keyword': '[0, "平台"]',
            'converted_must_keyword': '[0, "平台"]',
            'unlike_keyword': '0',
            'converted_unlike_keyword': '0',
            'expected': True
        },
        {
            'like_keyword': '[0, "软件"]',
            'converted_like_keyword': '[0, "软件"]',
            'must_keyword': '0',
            'converted_must_keyword': '0',
            'unlike_keyword': '0',
            'converted_unlike_keyword': '0',
            'expected': True
        }
    ]

    # 创建关键词行数据
    text_columns = ['company_profile', 'business_scope', 'main_product']

    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        try:
            keyword_row = {
                'like_keyword': test_case['like_keyword'],
                'converted_like_keyword': test_case['converted_like_keyword'],
                'must_keyword': test_case['must_keyword'],
                'converted_must_keyword': test_case['converted_must_keyword'],
                'unlike_keyword': test_case['unlike_keyword'],
                'converted_unlike_keyword': test_case['converted_unlike_keyword']
            }

            result = engine.match_keywords(
                keyword_row,
                test_data,
                text_columns
            )
            
            passed = result.success == test_case['expected']
            status = "✅ 通过" if passed else "❌ 失败"
            print(f"测试 {i}: {status}")
            print(f"   期望: {test_case['expected']}, 实际: {result.success}")
            
            if passed:
                success_count += 1
                
        except Exception as e:
            print(f"测试 {i}: ❌ 异常 - {e}")
    
    print(f"\n功能测试结果: {success_count}/{len(test_cases)} 通过")
    return success_count == len(test_cases)


def test_performance():
    """测试性能提升"""
    print("\n" + "=" * 60)
    print("性能提升验证测试")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 创建大量测试数据
    test_data_sets = []
    for i in range(100):
        test_data_sets.append({
            'lc_company_id': f'company_{i}',
            'company_name': f'企业{i}有限公司',
            'company_profile': np.array([f'企业{i}专注技术创新', f'提供专业{i}服务']),
            'business_scope': np.array([f'业务{i}开发', f'技术{i}咨询']),
            'main_product': np.array([f'产品{i}', f'解决方案{i}'])
        })
    
    # 性能测试
    keyword_row = {
        'like_keyword': '[0, "技术"]',
        'converted_like_keyword': '[0, "技术"]',
        'must_keyword': '0',
        'converted_must_keyword': '0',
        'unlike_keyword': '0',
        'converted_unlike_keyword': '0'
    }
    text_columns = ['company_profile', 'business_scope', 'main_product']

    print(f"准备测试 {len(test_data_sets)} 个企业数据...")

    # 获取初始内存
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024

    start_time = time.time()
    processed_count = 0
    matched_count = 0

    for test_data in test_data_sets:
        result = engine.match_keywords(keyword_row, test_data, text_columns)
        processed_count += 1
        if result.success:
            matched_count += 1
    
    end_time = time.time()
    final_memory = process.memory_info().rss / 1024 / 1024
    
    total_time = end_time - start_time
    memory_growth = final_memory - initial_memory
    
    print(f"性能测试结果:")
    print(f"- 处理企业数: {processed_count}")
    print(f"- 匹配企业数: {matched_count}")
    print(f"- 总处理时间: {total_time:.3f} 秒")
    print(f"- 平均处理时间: {(total_time/processed_count)*1000:.3f} ms/企业")
    print(f"- 处理速度: {processed_count/total_time:.0f} 企业/秒")
    print(f"- 内存增长: {memory_growth:.2f} MB")
    
    # 性能基准
    performance_good = (
        total_time < 5.0 and  # 总时间小于5秒
        memory_growth < 50.0  # 内存增长小于50MB
    )
    
    if performance_good:
        print("✅ 性能表现良好")
        return True
    else:
        print("❌ 性能可能需要进一步优化")
        return False


def check_source_code():
    """检查源代码是否已清理"""
    print("\n" + "=" * 60)
    print("源代码清理检查")
    print("=" * 60)
    
    # 读取源代码
    with open('core/matching_engine.py', 'r', encoding='utf-8') as f:
        source_code = f.read()
    
    # 检查是否还有被删除方法的定义
    removed_method_signatures = [
        'def _clean_and_validate_text',
        'def _fast_keyword_prefilter',
        'def _extract_simple_keywords_for_prefilter'
    ]
    
    found_signatures = []
    for signature in removed_method_signatures:
        if signature in source_code:
            found_signatures.append(signature)
    
    # 检查是否还有对这些方法的调用
    removed_method_calls = [
        '_clean_and_validate_text(',
        '_fast_keyword_prefilter(',
        '_extract_simple_keywords_for_prefilter('
    ]
    
    found_calls = []
    for call in removed_method_calls:
        if call in source_code:
            found_calls.append(call)
    
    print("源代码清理检查结果:")
    if found_signatures:
        print(f"❌ 发现未删除的方法定义: {found_signatures}")
    else:
        print("✅ 所有目标方法定义已删除")
    
    if found_calls:
        print(f"❌ 发现未删除的方法调用: {found_calls}")
    else:
        print("✅ 所有目标方法调用已删除")
    
    return len(found_signatures) == 0 and len(found_calls) == 0


def main():
    """主函数"""
    print("🚀 性能优化验证测试")
    print("=" * 60)
    print("验证删除 _clean_and_validate_text 和 _fast_keyword_prefilter 方法后的效果")
    print()
    
    # 执行所有测试
    tests = [
        ("方法删除验证", test_method_removal),
        ("缓存属性清理", test_cache_attributes),
        ("核心功能验证", test_functionality),
        ("性能提升验证", test_performance),
        ("源代码清理检查", check_source_code)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    # 最终评估
    print("\n" + "=" * 60)
    print("最终评估结果")
    print("=" * 60)
    
    print(f"测试通过率: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 性能优化完全成功！")
        print("✅ 删除了重复的文本处理操作")
        print("✅ 移除了低效益的预过滤机制")
        print("✅ 清理了无效的缓存机制")
        print("✅ 保持了核心功能的完整性")
        print("✅ 实现了性能提升")
    else:
        print("⚠️  性能优化可能不完整，需要进一步检查")


if __name__ == "__main__":
    main()
