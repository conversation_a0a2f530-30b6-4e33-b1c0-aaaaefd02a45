#!/usr/bin/env python3
"""
缓存机制修复验证脚本
Cache Mechanism Fix Verification Script

用于验证 _smart_parse_text_data 方法的缓存机制修复效果
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path
import psutil
import time
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.matching_engine import MatchingEngine


def create_test_data() -> List[Dict[str, Any]]:
    """创建测试用的企业数据，模拟真实的企业信源信息格式"""
    test_data = []
    
    for i in range(100):  # 创建100个企业的测试数据
        # 模拟企业信源信息 - 每个企业的信息都不同
        company_data = {
            'lc_company_id': f'company_{i}',
            'company_name': f'测试企业{i}有限公司',
            # 模拟numpy.ndarray格式的信源信息
            'company_profile': np.array([f'企业{i}专注于技术创新', f'成立于202{i%10}年']),
            'business_scope': np.array([f'业务范围{i}包含软件开发', f'技术服务{i}']),
            'main_product': [f'主要产品{i}', f'核心技术{i}'],
            # 模拟其他格式的数据
            'stock_introduction': f'股票简介{i}',
            'industry_l1_code': 'C36'
        }
        test_data.append(company_data)
    
    return test_data


def test_cache_behavior_before_fix():
    """测试修复前的缓存行为（理论上的问题行为）"""
    print("=" * 60)
    print("测试缓存机制修复效果")
    print("=" * 60)
    
    # 创建匹配引擎实例
    engine = MatchingEngine(use_shared_cache=False)
    
    # 获取初始内存使用情况
    process = psutil.Process()
    initial_memory = process.memory_info().rss / 1024 / 1024  # MB
    
    print(f"初始内存使用: {initial_memory:.2f} MB")
    print(f"初始解析缓存大小: {len(engine._text_parsing_cache)}")
    print(f"初始清理缓存大小: {len(engine._text_cleaning_cache)}")
    
    # 创建测试数据
    test_data = create_test_data()
    print(f"创建了 {len(test_data)} 个企业的测试数据")
    
    # 测试解析不同企业的信源信息
    start_time = time.time()
    
    for i, company_data in enumerate(test_data):
        # 测试不同类型的数据解析
        for field_name, field_data in company_data.items():
            if field_name not in ['lc_company_id', 'industry_l1_code']:
                # 调用 _smart_parse_text_data 方法
                parsed_result = engine._smart_parse_text_data(field_data)
                
        # 每处理10个企业，检查一次内存和缓存状态
        if (i + 1) % 10 == 0:
            current_memory = process.memory_info().rss / 1024 / 1024
            cache_size = len(engine._text_parsing_cache)
            print(f"处理 {i+1} 个企业后:")
            print(f"  内存使用: {current_memory:.2f} MB (增长: {current_memory - initial_memory:.2f} MB)")
            print(f"  解析缓存大小: {cache_size}")
            print(f"  缓存命中率: {engine._text_cache_hit_count}/{engine._text_cache_hit_count + engine._text_cache_miss_count}")
    
    end_time = time.time()
    final_memory = process.memory_info().rss / 1024 / 1024
    
    print("\n" + "=" * 40)
    print("测试结果总结:")
    print("=" * 40)
    print(f"处理时间: {end_time - start_time:.2f} 秒")
    print(f"内存增长: {final_memory - initial_memory:.2f} MB")
    print(f"最终解析缓存大小: {len(engine._text_parsing_cache)}")
    print(f"最终清理缓存大小: {len(engine._text_cleaning_cache)}")
    print(f"总缓存命中次数: {engine._text_cache_hit_count}")
    print(f"总缓存未命中次数: {engine._text_cache_miss_count}")
    
    if engine._text_cache_hit_count + engine._text_cache_miss_count > 0:
        hit_rate = engine._text_cache_hit_count / (engine._text_cache_hit_count + engine._text_cache_miss_count) * 100
        print(f"缓存命中率: {hit_rate:.2f}%")
    
    return {
        'memory_growth': final_memory - initial_memory,
        'cache_size': len(engine._text_parsing_cache),
        'hit_rate': hit_rate if engine._text_cache_hit_count + engine._text_cache_miss_count > 0 else 0,
        'processing_time': end_time - start_time
    }


def test_specific_data_types():
    """测试特定数据类型的缓存行为"""
    print("\n" + "=" * 60)
    print("测试特定数据类型的缓存行为")
    print("=" * 60)
    
    engine = MatchingEngine(use_shared_cache=False)
    
    # 测试不同类型的数据
    test_cases = [
        ("简单字符串", "技术服务"),
        ("长字符串", "这是一个很长的企业描述信息，包含了很多详细的业务范围和技术能力介绍"),
        ("numpy数组", np.array(["软件开发", "技术咨询"])),
        ("Python列表", ["产品研发", "市场营销"]),
        ("重复简单字符串", "技术服务"),  # 不应该缓存（不是默认值）
        ("重复numpy数组", np.array(["软件开发", "技术咨询"])),  # 不应该缓存
        ("默认值字符串", "default"),  # 应该缓存
        ("数字0", "0"),  # 应该缓存
        ("重复默认值", "default"),  # 应该命中缓存
        ("数字类型", 123),  # 应该缓存
        ("重复数字", 123),  # 应该命中缓存
    ]
    
    print("测试各种数据类型的缓存行为:")
    for i, (desc, data) in enumerate(test_cases):
        initial_cache_size = len(engine._text_parsing_cache)
        initial_hits = engine._text_cache_hit_count
        
        result = engine._smart_parse_text_data(data)
        
        final_cache_size = len(engine._text_parsing_cache)
        final_hits = engine._text_cache_hit_count
        
        cache_added = final_cache_size > initial_cache_size
        cache_hit = final_hits > initial_hits
        
        print(f"{i+1}. {desc}:")
        print(f"   数据类型: {type(data)}")
        print(f"   解析结果: {result}")
        print(f"   是否添加到缓存: {'是' if cache_added else '否'}")
        print(f"   是否命中缓存: {'是' if cache_hit else '否'}")
        print()


def main():
    """主函数"""
    print("🔧 缓存机制修复验证测试")
    print("=" * 60)
    print("本测试用于验证 _smart_parse_text_data 方法的缓存机制修复效果")
    print("修复内容：移除对企业信源信息（numpy.ndarray等）的不必要缓存")
    print()
    
    # 测试1：整体缓存行为
    results = test_cache_behavior_before_fix()
    
    # 测试2：特定数据类型
    test_specific_data_types()
    
    # 分析结果
    print("=" * 60)
    print("修复效果分析:")
    print("=" * 60)
    
    if results['hit_rate'] < 10:  # 缓存命中率低于10%
        print("✅ 修复成功：缓存命中率很低，说明不再对企业信源信息进行无效缓存")
    else:
        print("❌ 可能存在问题：缓存命中率较高，需要进一步检查")
    
    if results['memory_growth'] < 50:  # 内存增长小于50MB
        print("✅ 修复成功：内存增长控制良好")
    else:
        print("⚠️  注意：内存增长较大，可能需要进一步优化")
    
    if results['cache_size'] < 100:  # 缓存大小小于100
        print("✅ 修复成功：缓存大小控制合理")
    else:
        print("⚠️  注意：缓存大小较大，可能存在不必要的缓存")
    
    print(f"\n总体评估：")
    print(f"- 内存增长: {results['memory_growth']:.2f} MB")
    print(f"- 缓存大小: {results['cache_size']}")
    print(f"- 缓存命中率: {results['hit_rate']:.2f}%")
    print(f"- 处理时间: {results['processing_time']:.2f} 秒")


if __name__ == "__main__":
    main()
