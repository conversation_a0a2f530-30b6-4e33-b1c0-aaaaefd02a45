#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实生产数据的性能测试

使用真实的207个关键词规则和5000条企业数据进行准确的性能评估
"""

import time
import sys
import os
import logging
from typing import Dict, Any, List, Optional
import json
import pandas as pd
from pathlib import Path
import random

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.keyword_matcher import KeywordMatcher
from core.matching_engine import MatchingEngine
from config.manager import config_manager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealDataPerformanceTester:
    """基于真实数据的性能测试器"""
    
    def __init__(self):
        self.logger = logger
        self.matcher = None
        self.keyword_data = None
        self.enterprise_data = None

    def preprocess_enterprise_data(self, df):
        """预处理企业数据，将数组格式转换为字符串"""
        self.logger.info("🔧 预处理企业数据格式...")

        # 获取文本列配置
        text_columns = config_manager.get_list('keyword_matching.input_table_columns.text_content_columns')
        available_text_columns = [col for col in text_columns if col in df.columns]

        for col in available_text_columns:
            if col in df.columns:
                # 处理数组格式的数据
                def convert_array_to_string(value):
                    try:
                        if value is None or (isinstance(value, float) and pd.isna(value)):
                            return ""

                        # 如果是数组或列表，转换为字符串
                        if hasattr(value, '__iter__') and not isinstance(value, str):
                            # 过滤掉空值和无效值
                            valid_items = []
                            for item in value:
                                if item is not None and str(item).strip():
                                    valid_items.append(str(item).strip())
                            return " ".join(valid_items)
                        else:
                            return str(value).strip()
                    except Exception as e:
                        self.logger.debug(f"转换数据失败: {e}")
                        return ""

                df[col] = df[col].apply(convert_array_to_string)
                self.logger.debug(f"  处理列 {col}: 完成")

        return df
        
    def load_real_keyword_data(self) -> pd.DataFrame:
        """加载真实的207个关键词规则"""
        self.logger.info("🔑 加载真实关键词规则数据...")
        
        try:
            # 自动检测关键词文件
            keyword_files = []
            for file in os.listdir('.'):
                if file.startswith('converted_keyword_rules') and file.endswith('.xlsx'):
                    keyword_files.append(file)
            
            if not keyword_files:
                raise FileNotFoundError("未找到关键词规则文件（converted_keyword_rules*.xlsx）")
            
            # 选择最新的关键词文件
            keyword_file = max(keyword_files, key=lambda f: os.path.getmtime(f))
            self.logger.info(f"使用关键词文件: {keyword_file}")
            
            # 加载关键词数据
            df = pd.read_excel(keyword_file, sheet_name='转换后规则')
            self.logger.info(f"成功加载关键词数据: {len(df)} 条规则")
            
            return df
            
        except Exception as e:
            self.logger.error(f"加载关键词数据失败: {e}")
            raise
    
    def load_real_enterprise_data(self, target_count: int = 5000) -> pd.DataFrame:
        """加载真实的企业数据"""
        self.logger.info(f"🏢 加载真实企业数据 (目标: {target_count} 条)...")
        
        try:
            # 获取数据文件夹路径
            folder_path_1 = config_manager.get_str('keyword_matching.parquet_data_source.folder_path_1')
            folder_path_2 = config_manager.get_str('keyword_matching.parquet_data_source.folder_path_2')
            
            # 转换为相对路径
            folder_path_1 = folder_path_1.replace('F:/蕾奥工作/11.自研挂链/', '../')
            folder_path_2 = folder_path_2.replace('F:/蕾奥工作/11.自研挂链/', '../')
            
            # 获取可用的parquet文件
            available_files = []
            for folder in [folder_path_1, folder_path_2]:
                if os.path.exists(folder):
                    files = [f for f in os.listdir(folder) if f.endswith('.parquet')]
                    available_files.extend([(folder, f) for f in files])
            
            if not available_files:
                raise FileNotFoundError("未找到可用的parquet文件")
            
            self.logger.info(f"找到 {len(available_files)} 个parquet文件")
            
            # 随机选择文件并加载数据，直到达到目标数量
            combined_data = []
            loaded_count = 0
            
            # 随机打乱文件顺序
            random.shuffle(available_files)
            
            for folder, filename in available_files:
                if loaded_count >= target_count:
                    break
                
                file_path = os.path.join(folder, filename)
                try:
                    df = pd.read_parquet(file_path)
                    
                    # 随机采样以避免数据偏差
                    if len(df) > 0:
                        sample_size = min(len(df), target_count - loaded_count)
                        if sample_size < len(df):
                            df = df.sample(n=sample_size, random_state=42)
                        
                        combined_data.append(df)
                        loaded_count += len(df)
                        
                        self.logger.info(f"已加载 {loaded_count}/{target_count} 条企业数据")
                        
                except Exception as e:
                    self.logger.warning(f"加载文件 {filename} 失败: {e}")
                    continue
            
            if not combined_data:
                raise ValueError("未能加载任何企业数据")
            
            # 合并所有数据
            final_data = pd.concat(combined_data, ignore_index=True)

            # 预处理企业数据
            final_data = self.preprocess_enterprise_data(final_data)

            # 确保不超过目标数量
            if len(final_data) > target_count:
                final_data = final_data.sample(n=target_count, random_state=42)

            self.logger.info(f"成功加载企业数据: {len(final_data)} 条记录")
            return final_data
            
        except Exception as e:
            self.logger.error(f"加载企业数据失败: {e}")
            raise
    
    def test_real_data_performance(self, enterprise_count: int = 5000) -> Dict[str, Any]:
        """测试真实数据的性能"""
        self.logger.info(f"🚀 开始真实数据性能测试 (企业数量: {enterprise_count})...")
        
        try:
            # 加载真实数据
            self.logger.info("步骤1: 加载关键词规则...")
            self.keyword_data = self.load_real_keyword_data()
            
            self.logger.info("步骤2: 加载企业数据...")
            self.enterprise_data = self.load_real_enterprise_data(enterprise_count)
            
            # 初始化匹配器
            self.logger.info("步骤3: 初始化匹配引擎...")
            self.matcher = KeywordMatcher()
            
            # 获取文本列配置
            text_columns = config_manager.get_list('keyword_matching.input_table_columns.text_content_columns')
            
            # 过滤存在的文本列
            available_text_columns = [col for col in text_columns if col in self.enterprise_data.columns]
            self.logger.info(f"可用文本列: {available_text_columns}")
            
            if not available_text_columns:
                raise ValueError("企业数据中未找到可用的文本列")
            
            # 开始性能测试
            self.logger.info("步骤4: 开始匹配性能测试...")
            start_time = time.time()
            
            # 统计信息
            total_combinations = len(self.keyword_data) * len(self.enterprise_data)
            successful_matches = 0
            failed_matches = 0
            total_matched_texts = 0
            
            # 逐个处理关键词规则
            for idx, keyword_row in self.keyword_data.iterrows():
                keyword_start_time = time.time()
                
                # 处理每个企业
                for _, enterprise_row in self.enterprise_data.iterrows():
                    try:
                        # 执行匹配
                        result = self.matcher.matching_engine.match_keywords(
                            keyword_row.to_dict(),
                            enterprise_row.to_dict(),
                            available_text_columns
                        )
                        
                        if result.success:
                            successful_matches += 1
                            # 统计匹配到的文本数量
                            for col_texts in result.matched_texts.values():
                                total_matched_texts += len(col_texts)
                        else:
                            failed_matches += 1
                            
                    except Exception as e:
                        failed_matches += 1
                        self.logger.debug(f"匹配失败: {e}")
                
                # 每处理10个关键词报告一次进度
                if (idx + 1) % 10 == 0:
                    elapsed = time.time() - start_time
                    progress = (idx + 1) / len(self.keyword_data) * 100
                    estimated_total = elapsed / progress * 100
                    remaining = estimated_total - elapsed
                    
                    self.logger.info(f"进度: {idx + 1}/{len(self.keyword_data)} 关键词 "
                                   f"({progress:.1f}%), "
                                   f"已用时: {elapsed:.1f}秒, "
                                   f"预计剩余: {remaining:.1f}秒")
            
            total_time = time.time() - start_time
            
            # 计算性能指标
            combinations_per_second = total_combinations / total_time if total_time > 0 else 0
            enterprises_per_second = len(self.enterprise_data) * len(self.keyword_data) / total_time if total_time > 0 else 0
            
            # 推算10万企业的处理时间
            estimated_100k_time_hours = (total_time * 100000 / len(self.enterprise_data)) / 3600
            
            result = {
                'test_name': '真实数据性能测试',
                'enterprise_count': len(self.enterprise_data),
                'keyword_count': len(self.keyword_data),
                'total_combinations': total_combinations,
                'total_time_seconds': total_time,
                'successful_matches': successful_matches,
                'failed_matches': failed_matches,
                'total_matched_texts': total_matched_texts,
                'combinations_per_second': combinations_per_second,
                'enterprises_per_second': enterprises_per_second,
                'avg_time_per_enterprise_ms': (total_time / len(self.enterprise_data)) * 1000,
                'estimated_100k_enterprises_hours': estimated_100k_time_hours,
                'success_rate_percent': (successful_matches / total_combinations * 100) if total_combinations > 0 else 0,
                'available_text_columns': available_text_columns,
                'text_columns_count': len(available_text_columns)
            }
            
            self.logger.info(f"✅ 真实数据测试完成!")
            self.logger.info(f"总处理时间: {total_time:.2f} 秒")
            self.logger.info(f"成功匹配: {successful_matches}/{total_combinations} ({result['success_rate_percent']:.1f}%)")
            self.logger.info(f"匹配速度: {combinations_per_second:.0f} 组合/秒")
            self.logger.info(f"预估10万企业处理时间: {estimated_100k_time_hours:.2f} 小时")
            
            return result
            
        except Exception as e:
            self.logger.error(f"真实数据性能测试失败: {e}")
            return {'test_name': '真实数据性能测试', 'error': str(e)}
    
    def compare_with_previous_results(self, current_result: Dict[str, Any]) -> Dict[str, Any]:
        """与之前的模拟数据结果进行对比"""
        self.logger.info("📊 对比分析...")
        
        # 之前模拟数据的结果（从之前的测试中获得）
        simulated_result = {
            'estimated_100k_enterprises_hours': 0.016,  # 之前模拟数据的预估
            'operations_per_second': 15452
        }
        
        current_hours = current_result.get('estimated_100k_enterprises_hours', 0)
        current_ops_per_sec = current_result.get('combinations_per_second', 0)
        
        # 计算差异
        time_difference_factor = current_hours / simulated_result['estimated_100k_enterprises_hours'] if simulated_result['estimated_100k_enterprises_hours'] > 0 else float('inf')
        speed_difference_factor = simulated_result['operations_per_second'] / current_ops_per_sec if current_ops_per_sec > 0 else float('inf')
        
        comparison = {
            'simulated_vs_real_time_factor': time_difference_factor,
            'simulated_vs_real_speed_factor': speed_difference_factor,
            'real_data_slower_by_hours': current_hours - simulated_result['estimated_100k_enterprises_hours'],
            'assessment': self._assess_performance_gap(time_difference_factor)
        }
        
        self.logger.info(f"性能对比结果:")
        self.logger.info(f"  模拟数据预估: {simulated_result['estimated_100k_enterprises_hours']:.3f} 小时")
        self.logger.info(f"  真实数据预估: {current_hours:.3f} 小时")
        self.logger.info(f"  真实数据慢了: {time_difference_factor:.1f} 倍")
        self.logger.info(f"  评估: {comparison['assessment']}")
        
        return comparison
    
    def _assess_performance_gap(self, factor: float) -> str:
        """评估性能差距"""
        if factor <= 2:
            return "差距较小，模拟数据预估基本准确"
        elif factor <= 5:
            return "存在一定差距，需要进一步优化"
        elif factor <= 10:
            return "差距较大，需要重新评估优化策略"
        else:
            return "差距巨大，模拟数据预估严重偏离实际"
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        self.logger.info("🚀 开始基于真实数据的综合性能测试...")
        
        # 运行真实数据测试
        real_data_result = self.test_real_data_performance(5000)
        
        # 与模拟数据结果对比
        comparison = self.compare_with_previous_results(real_data_result)
        
        # 生成最终报告
        results = {
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
            'real_data_test': real_data_result,
            'comparison_analysis': comparison,
            'summary': self._generate_summary(real_data_result, comparison)
        }
        
        return results
    
    def _generate_summary(self, real_result: Dict[str, Any], comparison: Dict[str, Any]) -> Dict[str, Any]:
        """生成测试摘要"""
        estimated_hours = real_result.get('estimated_100k_enterprises_hours', 0)
        
        if estimated_hours < 2:
            performance_level = "🎉 优秀"
            recommendation = "性能达标，建议部署到生产环境"
        elif estimated_hours < 5:
            performance_level = "✅ 良好"
            recommendation = "性能接近目标，可考虑部署并持续监控"
        elif estimated_hours < 10:
            performance_level = "⚠️ 一般"
            recommendation = "需要进一步优化后再部署"
        else:
            performance_level = "❌ 不达标"
            recommendation = "性能严重不足，需要重新设计优化策略"
        
        return {
            'performance_level': performance_level,
            'estimated_100k_hours': estimated_hours,
            'vs_target_2hours': f"{estimated_hours/2:.1f}x" if estimated_hours > 0 else "N/A",
            'recommendation': recommendation,
            'key_findings': [
                f"真实数据处理比模拟数据慢 {comparison.get('simulated_vs_real_time_factor', 0):.1f} 倍",
                f"成功匹配率: {real_result.get('success_rate_percent', 0):.1f}%",
                f"处理速度: {real_result.get('combinations_per_second', 0):.0f} 组合/秒"
            ]
        }

def main():
    """主函数"""
    print("🚀 基于真实生产数据的性能测试")
    print("=" * 60)
    
    tester = RealDataPerformanceTester()
    
    try:
        # 运行综合测试
        results = tester.run_comprehensive_test()
        
        # 保存测试结果
        output_file = f"real_data_performance_test_{int(time.time())}.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📊 测试结果已保存到: {output_file}")
        
        # 打印摘要
        summary = results['summary']
        print(f"\n📈 真实数据性能测试摘要:")
        print("-" * 50)
        print(f"🎯 性能等级: {summary['performance_level']}")
        print(f"⏱️ 预估10万企业处理时间: {summary['estimated_100k_hours']:.2f} 小时")
        print(f"📊 相对于2小时目标: {summary['vs_target_2hours']}")
        print(f"💡 建议: {summary['recommendation']}")
        
        print(f"\n🔍 关键发现:")
        for finding in summary['key_findings']:
            print(f"  • {finding}")
            
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
