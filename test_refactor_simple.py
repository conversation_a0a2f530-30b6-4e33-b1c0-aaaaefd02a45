#!/usr/bin/env python3
"""
简化的重构验证测试
Simple Refactor Verification Test

直接检查源代码文件验证重构效果
"""

import json
from pathlib import Path


def test_cli_source_code():
    """测试CLI源代码重构效果"""
    print("=" * 60)
    print("CLI源代码重构验证")
    print("=" * 60)
    
    # 读取CLI源代码
    cli_file = Path("cli/keyword_matcher_cli.py")
    if not cli_file.exists():
        print("❌ CLI文件不存在")
        return False
    
    with open(cli_file, 'r', encoding='utf-8') as f:
        source = f.read()
    
    # 检查已删除的代码
    removed_code = [
        'parquet_enabled = self.matcher.parquet_enabled',
        'if parquet_enabled:',
        'and parquet_enabled',
        'max_choice = 3',
        'max_choice = 6'
    ]
    
    found_removed = []
    for code in removed_code:
        if code in source:
            found_removed.append(code)
    
    # 检查应该保留的代码
    required_code = [
        'print("4. 从Parquet文件夹选择单个文件")',
        'print("5. 批量处理所有Parquet文件")',
        'print("6. 随机选择Parquet文件测试")',
        'choice = input("\\n请选择 [1-6]: ").strip()',
        "elif choice == '4':",
        "elif choice == '5':",
        "elif choice == '6':"
    ]
    
    missing_required = []
    for code in required_code:
        if code not in source:
            missing_required.append(code)
    
    print("CLI代码检查结果:")
    if found_removed:
        print(f"❌ 发现应该删除的代码: {found_removed}")
    else:
        print("✅ 已删除所有条件分支代码")
    
    if missing_required:
        print(f"❌ 缺少必要的代码: {missing_required}")
    else:
        print("✅ 保留了所有必要的功能代码")
    
    # 检查选项显示
    options_correct = (
        'print("• 支持Parquet文件夹数据源")' in source and
        'print("• Parquet数据源未启用")' not in source
    )
    
    if options_correct:
        print("✅ Parquet数据源信息显示正确")
    else:
        print("❌ Parquet数据源信息显示有问题")
    
    return len(found_removed) == 0 and len(missing_required) == 0 and options_correct


def test_matcher_source_code():
    """测试Matcher源代码重构效果"""
    print("\n" + "=" * 60)
    print("Matcher源代码重构验证")
    print("=" * 60)
    
    # 读取Matcher源代码
    matcher_file = Path("core/keyword_matcher.py")
    if not matcher_file.exists():
        print("❌ Matcher文件不存在")
        return False
    
    with open(matcher_file, 'r', encoding='utf-8') as f:
        source = f.read()
    
    # 检查已删除的代码
    removed_code = [
        'self.parquet_enabled = self.parquet_config.get(\'enable\', False)',
        'if self.parquet_enabled:',
        'if not self.parquet_enabled:',
        'self.parquet_enabled = False',
        'and self.parquet_enabled'
    ]
    
    found_removed = []
    for code in removed_code:
        if code in source:
            found_removed.append(code)
    
    # 检查应该保留的代码
    required_code = [
        'self.folder_path_1 = self.parquet_config.get(\'folder_path_1\', \'\')',
        'self.folder_path_2 = self.parquet_config.get(\'folder_path_2\', \'\')',
        'self.merge_columns = self.parquet_config.get(\'merge_on_columns\'',
        'def load_parquet_data_from_folders',
        'def get_available_parquet_files',
        'def run_batch_processing',
        'def get_random_parquet_file'
    ]
    
    missing_required = []
    for code in required_code:
        if code not in source:
            missing_required.append(code)
    
    print("Matcher代码检查结果:")
    if found_removed:
        print(f"❌ 发现应该删除的代码: {found_removed}")
    else:
        print("✅ 已删除所有parquet_enabled检查")
    
    if missing_required:
        print(f"❌ 缺少必要的代码: {missing_required}")
    else:
        print("✅ 保留了所有Parquet功能方法")
    
    return len(found_removed) == 0 and len(missing_required) == 0


def test_config_file():
    """测试配置文件修改"""
    print("\n" + "=" * 60)
    print("配置文件修改验证")
    print("=" * 60)
    
    # 读取配置文件
    config_file = Path("config.json")
    if not config_file.exists():
        print("❌ 配置文件不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查parquet_data_source配置
        parquet_config = config.get('keyword_matching', {}).get('parquet_data_source', {})
        
        has_enable = 'enable' in parquet_config
        has_folder_paths = 'folder_path_1' in parquet_config and 'folder_path_2' in parquet_config
        has_merge_columns = 'merge_on_columns' in parquet_config
        
        print("配置文件检查结果:")
        if has_enable:
            print("❌ 仍然存在 'enable' 配置项")
        else:
            print("✅ 已删除 'enable' 配置项")
        
        if has_folder_paths:
            print("✅ 保留了文件夹路径配置")
        else:
            print("❌ 缺少文件夹路径配置")
        
        if has_merge_columns:
            print("✅ 保留了合并列配置")
        else:
            print("❌ 缺少合并列配置")
        
        print(f"Parquet配置内容:")
        for key, value in parquet_config.items():
            if key != '_comments':
                print(f"   {key}: {value}")
        
        return not has_enable and has_folder_paths and has_merge_columns
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False


def test_code_statistics():
    """测试代码统计"""
    print("\n" + "=" * 60)
    print("代码统计分析")
    print("=" * 60)
    
    # 统计CLI文件
    cli_file = Path("cli/keyword_matcher_cli.py")
    if cli_file.exists():
        with open(cli_file, 'r', encoding='utf-8') as f:
            cli_lines = f.readlines()
        
        # 找到get_input_file_path方法
        method_start = -1
        method_end = -1
        indent_level = 0
        
        for i, line in enumerate(cli_lines):
            if 'def get_input_file_path(self)' in line:
                method_start = i
                indent_level = len(line) - len(line.lstrip())
                continue
            
            if method_start != -1 and i > method_start:
                current_indent = len(line) - len(line.lstrip()) if line.strip() else float('inf')
                if line.strip() and current_indent <= indent_level:
                    method_end = i
                    break
        
        if method_start != -1:
            if method_end == -1:
                method_end = len(cli_lines)
            
            method_lines = cli_lines[method_start:method_end]
            total_lines = len(method_lines)
            non_empty_lines = len([line for line in method_lines if line.strip()])
            comment_lines = len([line for line in method_lines if line.strip().startswith('#')])
            
            # 统计条件语句
            if_count = sum(1 for line in method_lines if 'if ' in line.strip())
            elif_count = sum(1 for line in method_lines if 'elif ' in line.strip())
            else_count = sum(1 for line in method_lines if line.strip() == 'else:')
            
            print("get_input_file_path方法统计:")
            print(f"- 总行数: {total_lines}")
            print(f"- 有效代码行: {non_empty_lines}")
            print(f"- 注释行: {comment_lines}")
            print(f"- if语句: {if_count}")
            print(f"- elif语句: {elif_count}")
            print(f"- else语句: {else_count}")
            
            # 简单的复杂度评估
            complexity = if_count + elif_count + else_count
            print(f"- 条件复杂度: {complexity}")
            
            # 对于用户交互方法，适当放宽复杂度标准
            if complexity < 30:
                print("✅ 代码复杂度合理")
                return True
            else:
                print("⚠️  代码复杂度较高")
                return False
        else:
            print("❌ 未找到get_input_file_path方法")
            return False
    else:
        print("❌ CLI文件不存在")
        return False


def main():
    """主函数"""
    print("🔧 输入路径获取方法重构验证测试")
    print("=" * 60)
    print("验证 get_input_file_path 方法重构后的效果")
    print()
    
    # 执行所有测试
    tests = [
        ("CLI源代码重构", test_cli_source_code),
        ("Matcher源代码重构", test_matcher_source_code),
        ("配置文件修改", test_config_file),
        ("代码统计分析", test_code_statistics)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} 执行异常: {e}")
    
    # 最终评估
    print("\n" + "=" * 60)
    print("最终评估结果")
    print("=" * 60)
    
    print(f"测试通过率: {passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print("🎉 方法重构完全成功！")
        print("✅ 删除了 parquet_enabled 参数检查")
        print("✅ 固定选项数量为6个")
        print("✅ 简化了条件判断")
        print("✅ 清理了配置文件")
        print("✅ 保持了功能完整性")
    elif passed_tests >= total_tests * 0.75:
        print("✅ 方法重构基本成功")
        print("大部分目标已达成，可能需要微调")
    else:
        print("⚠️  方法重构需要进一步调整")


if __name__ == "__main__":
    main()
