# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# Data files
*.xlsx
*.xls
*.csv
*.tsv
*.json
*.parquet
*.pkl
*.pickle
*.h5
*.hdf5
*.db
*.sqlite
*.sqlite3

# Log files
*.log
logs/
log/

# Output files
results/
output/
outputs/
temp/
tmp/
cache/

# Configuration files with sensitive data
config_local.py
config_prod.py
secrets.json
.secrets

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Backup files
*.bak
*.backup
*.old
*.orig

# Compressed files
*.zip
*.rar
*.7z
*.tar.gz
*.tar.bz2

# Large model files
*.model
*.bin
*.pt
*.pth
*.ckpt

# Documentation build
docs/_build/
docs/build/

# Test data
test_data/
sample_data/
mock_data/

# Specific to this project
converted_keyword_rules*.xlsx
matching_results*.csv
matching_results*.xlsx
summary_report*.json
test_results.*
performance_test_*
benchmark_*

# Input data directories (uncomment if needed)
# 输入数据整理/
# result_date_part*/
# batch_*.parquet
