#!/usr/bin/env python3
"""
进度条显示模块
Progress Bar Display Module

提供实时进度条显示功能，支持百分比、数量统计、预估时间等信息。
设计为轻量级、高性能的控制台进度显示工具。

作者：系统开发
日期：2024年
"""

import sys
import time
import threading
from datetime import datetime, timedelta
from typing import Optional, Callable


class ProgressBar:
    """
    实时进度条显示类
    
    特性：
    - 显示百分比进度
    - 显示已处理/总计数量（支持千分位分隔符）
    - 显示预估剩余时间
    - 使用简洁的文本进度条格式
    - 线程安全的更新机制
    - 可配置的更新频率
    """
    
    def __init__(self, total: int, description: str = "处理中", 
                 bar_length: int = 20, update_interval: float = 0.5):
        """
        初始化进度条
        
        Args:
            total: 总任务数量
            description: 进度描述文本
            bar_length: 进度条长度（字符数）
            update_interval: 更新间隔（秒）
        """
        self.total = total
        self.description = description
        self.bar_length = bar_length
        self.update_interval = update_interval
        
        # 进度状态
        self.current = 0
        self.start_time = None
        self.last_update_time = 0
        self.is_running = False
        self.is_finished = False
        
        # 线程安全锁
        self._lock = threading.Lock()
        
        # 显示相关
        self._last_line_length = 0
        
    def start(self):
        """启动进度条"""
        with self._lock:
            self.start_time = time.time()
            self.is_running = True
            self.is_finished = False
            self.current = 0
            
        # 显示初始进度条
        self._display_progress()
    
    def update(self, current: Optional[int] = None, increment: int = 1):
        """
        更新进度
        
        Args:
            current: 当前进度值（如果提供，则直接设置；否则增加increment）
            increment: 增量值（当current为None时使用）
        """
        with self._lock:
            if not self.is_running:
                return
                
            if current is not None:
                self.current = current
            else:
                self.current += increment
                
            # 限制在有效范围内
            self.current = min(self.current, self.total)
            
            # 检查是否需要更新显示
            current_time = time.time()
            if (current_time - self.last_update_time >= self.update_interval or 
                self.current >= self.total):
                self.last_update_time = current_time
                self._display_progress()
                
            # 检查是否完成
            if self.current >= self.total and not self.is_finished:
                self.is_finished = True
    
    def finish(self, message: Optional[str] = None):
        """
        完成进度条显示
        
        Args:
            message: 完成时显示的消息
        """
        with self._lock:
            if not self.is_running:
                return
                
            self.current = self.total
            self.is_finished = True
            self.is_running = False
            
        # 显示最终进度
        self._display_progress()
        
        # 显示完成消息
        if message:
            print(f"\n{message}")
        else:
            print()  # 换行
    
    def _display_progress(self):
        """显示进度条"""
        if self.total <= 0:
            return
            
        # 计算进度百分比
        percentage = (self.current / self.total) * 100
        
        # 生成进度条
        filled_length = int(self.bar_length * self.current // self.total)
        bar = '█' * filled_length + '░' * (self.bar_length - filled_length)
        
        # 格式化数量（添加千分位分隔符）
        current_str = f"{self.current:,}"
        total_str = f"{self.total:,}"
        
        # 计算预估剩余时间
        time_info = self._calculate_time_info()
        
        # 构建进度行
        progress_line = (f"\r{self.description}: [{bar}] {percentage:.1f}% "
                        f"({current_str}/{total_str}){time_info}")
        
        # 清除之前的行（如果新行更短）
        if len(progress_line) < self._last_line_length:
            clear_line = "\r" + " " * self._last_line_length + "\r"
            sys.stdout.write(clear_line)
        
        # 输出进度行
        sys.stdout.write(progress_line)
        sys.stdout.flush()
        
        self._last_line_length = len(progress_line)
    
    def _calculate_time_info(self) -> str:
        """计算时间信息"""
        if not self.start_time or self.current <= 0:
            return ""
            
        elapsed_time = time.time() - self.start_time
        
        if self.current >= self.total:
            # 已完成，显示总耗时
            return f" 耗时: {self._format_duration(elapsed_time)}"
        
        # 计算预估剩余时间
        if elapsed_time > 0:
            rate = self.current / elapsed_time
            if rate > 0:
                remaining_items = self.total - self.current
                estimated_remaining = remaining_items / rate
                return f" 预计剩余: {self._format_duration(estimated_remaining)}"
        
        return ""
    
    def _format_duration(self, seconds: float) -> str:
        """格式化时间长度"""
        if seconds < 60:
            return f"{int(seconds)}秒"
        elif seconds < 3600:
            minutes = int(seconds // 60)
            secs = int(seconds % 60)
            return f"{minutes}分{secs}秒"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}小时{minutes}分"


class ProgressBarManager:
    """
    进度条管理器
    
    用于管理多个进度条的显示，确保线程安全和正确的显示顺序。
    """
    
    def __init__(self):
        self._current_bar: Optional[ProgressBar] = None
        self._lock = threading.Lock()
    
    def create_progress_bar(self, total: int, description: str = "处理中", 
                           bar_length: int = 20, update_interval: float = 0.5) -> ProgressBar:
        """
        创建新的进度条
        
        Args:
            total: 总任务数量
            description: 进度描述文本
            bar_length: 进度条长度
            update_interval: 更新间隔
            
        Returns:
            ProgressBar: 进度条实例
        """
        with self._lock:
            # 如果有当前进度条，先完成它
            if self._current_bar and self._current_bar.is_running:
                self._current_bar.finish()
            
            # 创建新进度条
            progress_bar = ProgressBar(total, description, bar_length, update_interval)
            self._current_bar = progress_bar
            
            return progress_bar
    
    def get_current_bar(self) -> Optional[ProgressBar]:
        """获取当前活动的进度条"""
        return self._current_bar


# 全局进度条管理器实例
progress_manager = ProgressBarManager()


def create_progress_bar(total: int, description: str = "处理中", 
                       bar_length: int = 20, update_interval: float = 0.5) -> ProgressBar:
    """
    便捷函数：创建进度条
    
    Args:
        total: 总任务数量
        description: 进度描述文本
        bar_length: 进度条长度
        update_interval: 更新间隔
        
    Returns:
        ProgressBar: 进度条实例
    """
    return progress_manager.create_progress_bar(total, description, bar_length, update_interval)


if __name__ == "__main__":
    """测试进度条功能"""
    import random
    
    print("进度条功能测试")
    print("=" * 50)
    
    # 测试1: 基本进度条
    print("\n测试1: 基本进度条")
    total_items = 1000
    progress = create_progress_bar(total_items, "基本测试")
    progress.start()
    
    for i in range(total_items):
        time.sleep(0.001)  # 模拟处理时间
        progress.update()
    
    progress.finish("基本测试完成！")
    
    # 测试2: 大数量进度条
    print("\n测试2: 大数量进度条")
    total_items = 1000000
    progress = create_progress_bar(total_items, "大数量测试")
    progress.start()
    
    for i in range(0, total_items, 10000):
        time.sleep(0.01)  # 模拟批处理
        progress.update(i + 10000)
    
    progress.finish("大数量测试完成！")
    
    # 测试3: 不规则更新
    print("\n测试3: 不规则更新")
    total_items = 100
    progress = create_progress_bar(total_items, "不规则测试")
    progress.start()
    
    current = 0
    while current < total_items:
        increment = random.randint(1, 5)
        current = min(current + increment, total_items)
        progress.update(current)
        time.sleep(0.05)
    
    progress.finish("不规则测试完成！")
    
    print("\n所有测试完成！")
