    def _smart_parse_text_data(self, col_texts: Any) -> List[str]:
        """
        智能解析文本数据，支持多种格式的自动识别和转换

        支持的格式：
        1. 已经是列表格式：直接返回
        2. JSON字符串格式：如 '["文本1", "文本2"]'
        3. 单个字符串：转换为单元素列表

        注意：已移除自动分割功能，假设输入数据在预处理阶段已经处理完毕

        Args:
            col_texts: 原始文本数据，可能是字符串、列表或其他格式

        Returns:
            List[str]: 解析后的文本列表
        """
        # 如果已经是列表或数组，直接处理其中的元素（排除字符串）
        if isinstance(col_texts, (list, tuple)):
            result = []
            for item in col_texts:
                if item is not None:
                    item_str = str(item).strip()
                    if item_str:
                        result.append(item_str)
            return result
        elif hasattr(col_texts, '__iter__') and hasattr(col_texts, '__len__') and not isinstance(col_texts, (str, bytes)):
            # 处理numpy.ndarray和其他类似数组的对象（排除字符串和字节）
            try:
                result = []
                for item in col_texts:
                    if item is not None:
                        item_str = str(item).strip()
                        if item_str:
                            result.append(item_str)
                return result
            except Exception as e:
                self.logger.debug(f"处理数组类型数据时出错: {e}, 类型: {type(col_texts)}")
                # 回退到字符串处理
                pass

        # 处理空值情况（安全的空值检查）
        if col_texts is None:
            return []

        # 对于numpy数组等，检查长度
        try:
            if hasattr(col_texts, '__len__') and len(col_texts) == 0:
                return []
        except Exception:
            pass

        text_str = str(col_texts).strip()
        if not text_str:
            return []

        # 尝试JSON解析
        if self.text_parsing_config['enable_json_parsing']:
            try:
                parsed = json.loads(text_str)
                if isinstance(parsed, list):
                    result = []
                    for item in parsed:
                        if item and str(item).strip():
                            result.append(str(item).strip())
                    self.logger.debug(f"成功解析JSON格式文本数据，包含 {len(result)} 个元素")
                    return result
            except (json.JSONDecodeError, TypeError):
                # JSON解析失败，继续尝试其他方法
                pass

        # 默认情况：单个字符串（不进行分割）
        return [text_str]
